# LibreTV - Apple TV Video Player App

A modern, elegant video player app for Apple TV that provides access to a vast library of movies and TV shows through multiple streaming sources.

## 🎯 Features

### ✅ Completed Features
- **Modern Apple TV Interface**: Clean, TV-optimized UI with focus management
- **Home Screen**: Featured recommendations with horizontal carousels
- **Search Functionality**: Keyword-based search with multiple data sources
- **Movie Details**: Comprehensive movie information with episode selection
- **Video Player**: Full-screen video playback with remote control support
- **Multiple Sources**: Access to multiple streaming APIs for content variety
- **Backend API**: Enhanced Node.js server with dedicated Apple TV endpoints

### 🏗️ Architecture

#### Backend (LibreTV/)
- **Node.js/Express Server**: Enhanced with Apple TV-specific API endpoints
- **API Endpoints**:
  - `GET /api/recommendations` - Featured movies from multiple sources
  - `GET /api/search?q=query` - Search movies across sources
  - `GET /api/movie/:id?source=source` - Detailed movie information
  - `GET /api/categories` - Movie categories/genres
- **Multiple Data Sources**: Integrated with 5+ streaming APIs
- **CORS Enabled**: Ready for cross-origin requests

#### Apple TV App (TVBrowser/)
- **SwiftUI Framework**: Modern declarative UI
- **MVVM Architecture**: Clean separation of concerns
- **Models**: Movie, Episode, Category data structures
- **Services**: APIService for network communication
- **ViewModels**: HomeViewModel, SearchViewModel, MovieDetailViewModel
- **Views**: HomeView, SearchView, MovieDetailView, VideoPlayerView

## 🚀 Getting Started

### Prerequisites
- macOS with Xcode 15.0+
- Node.js 16.0+
- Apple TV Simulator

### Backend Setup
1. Navigate to the LibreTV directory:
   ```bash
   cd LibreTV
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the server:
   ```bash
   npm start
   ```
   
   The server will run on `http://localhost:8080`

### Apple TV App Setup
1. Open the Xcode project:
   ```bash
   open TVBrowser/TVBrowser.xcodeproj
   ```

2. Select Apple TV simulator as the target

3. Build and run the project (⌘+R)

## 📱 App Structure

### Home Screen
- **Featured Movies**: Horizontal carousel of recommended content
- **Categories**: Quick access to different movie genres
- **Recent Movies**: Latest additions to the library

### Search Screen
- **Real-time Search**: Debounced search with instant results
- **Source Selection**: Choose from multiple streaming sources
- **Grid Layout**: TV-optimized search results display

### Movie Detail Screen
- **Comprehensive Info**: Title, year, director, cast, description
- **Episode Selection**: For TV series with multiple episodes
- **Play Button**: Direct access to video playback

### Video Player
- **Full-screen Playback**: Immersive viewing experience
- **Remote Control Support**: Apple TV remote navigation
- **Progress Tracking**: Visual progress bar with time display
- **Playback Controls**: Play/pause, seek forward/backward

## 🛠️ Technical Details

### API Integration
- **Multiple Sources**: Integrated with 5 different streaming APIs
- **Error Handling**: Graceful fallbacks and error messages
- **Caching**: Optimized for performance
- **Real-time Data**: Live content updates

### tvOS Optimizations
- **Focus Management**: Proper focus handling for Apple TV remote
- **TV-Safe Areas**: Optimized layouts for television displays
- **Accessibility**: VoiceOver support and accessibility features
- **Performance**: Optimized for Apple TV hardware

### Video Playback
- **HLS Support**: HTTP Live Streaming (M3U8) compatibility
- **AVPlayer Integration**: Native iOS/tvOS video playback
- **Remote Control**: Full Apple TV remote support
- **Adaptive Streaming**: Automatic quality adjustment

## 🔧 Configuration

### Backend Configuration
Edit `LibreTV/js/config.js` to modify:
- Server port (default: 8080)
- API timeouts
- Debug settings
- User authentication

### App Configuration
Modify `TVBrowser/TVBrowser/Services/APIService.swift` to change:
- Backend server URL
- API endpoints
- Request timeouts

## 🎨 UI/UX Features

### Modern Design
- **Dark Theme**: Optimized for TV viewing
- **Smooth Animations**: Elegant transitions and focus effects
- **Typography**: TV-optimized font sizes and weights
- **Color Scheme**: High contrast for better visibility

### Navigation
- **Tab-based Navigation**: Home and Search tabs
- **Modal Presentations**: Movie details and video player
- **Focus Indicators**: Clear visual feedback for remote navigation

## 📊 Data Sources

The app integrates with multiple streaming APIs:
1. **黑木耳 (Heimuer)** - Primary source
2. **暴风资源 (Baofeng)** - Secondary source  
3. **如意资源 (Ruyi)** - Tertiary source
4. **非凡影视 (Feifan)** - Additional content
5. **天涯资源 (Tianya)** - Extended library

## 🚀 Future Enhancements

### Planned Features
- [ ] User Profiles and Favorites
- [ ] Watch History and Resume Playback
- [ ] Subtitle Support
- [ ] Multiple Audio Tracks
- [ ] Parental Controls
- [ ] Offline Downloads
- [ ] Social Features (Sharing, Reviews)
- [ ] Advanced Search Filters
- [ ] Recommendation Engine
- [ ] Multi-language Support

### Technical Improvements
- [ ] Core Data Integration
- [ ] CloudKit Sync
- [ ] Background App Refresh
- [ ] Push Notifications
- [ ] Analytics Integration
- [ ] Crash Reporting
- [ ] Performance Monitoring

## 📄 License

This project is for educational and personal use only. Please respect content licensing and copyright laws.

## 🤝 Contributing

This is a personal project, but suggestions and feedback are welcome!

---

**Built with ❤️ for Apple TV**
