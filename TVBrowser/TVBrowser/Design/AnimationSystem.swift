//
//  AnimationSystem.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI

// MARK: - Animation System

struct AnimationSystem {
    
    // MARK: - Custom Transitions
    
    static let slideFromRight = AnyTransition.asymmetric(
        insertion: .move(edge: .trailing).combined(with: .opacity),
        removal: .move(edge: .leading).combined(with: .opacity)
    )
    
    static let slideFromLeft = AnyTransition.asymmetric(
        insertion: .move(edge: .leading).combined(with: .opacity),
        removal: .move(edge: .trailing).combined(with: .opacity)
    )
    
    static let slideFromBottom = AnyTransition.asymmetric(
        insertion: .move(edge: .bottom).combined(with: .opacity),
        removal: .move(edge: .top).combined(with: .opacity)
    )
    
    static let scaleAndFade = AnyTransition.asymmetric(
        insertion: .scale(scale: 0.8).combined(with: .opacity),
        removal: .scale(scale: 1.2).combined(with: .opacity)
    )
    
    static let modalPresentation = AnyTransition.asymmetric(
        insertion: .scale(scale: 0.9).combined(with: .opacity),
        removal: .scale(scale: 0.9).combined(with: .opacity)
    )
    
    // MARK: - Focus Animations
    
    static func focusAnimation<T: Equatable>(value: T) -> Animation {
        .spring(response: 0.3, dampingFraction: 0.8, blendDuration: 0.1)
    }
    
    static func cardHoverAnimation() -> Animation {
        .spring(response: 0.4, dampingFraction: 0.7)
    }
    
    static func buttonPressAnimation() -> Animation {
        .easeInOut(duration: 0.1)
    }
    
    // MARK: - Loading Animations
    
    static func pulseAnimation() -> Animation {
        .easeInOut(duration: 1.0).repeatForever(autoreverses: true)
    }
    
    static func rotationAnimation() -> Animation {
        .linear(duration: 1.0).repeatForever(autoreverses: false)
    }
    
    static func breathingAnimation() -> Animation {
        .easeInOut(duration: 2.0).repeatForever(autoreverses: true)
    }
}

// MARK: - Custom Animation Modifiers

struct FocusAnimationModifier: ViewModifier {
    let isFocused: Bool
    let scale: CGFloat
    let shadowRadius: CGFloat
    
    init(isFocused: Bool, scale: CGFloat = 1.1, shadowRadius: CGFloat = 20) {
        self.isFocused = isFocused
        self.scale = scale
        self.shadowRadius = shadowRadius
    }
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isFocused ? scale : 1.0)
            .shadow(
                color: .white.opacity(isFocused ? 0.3 : 0),
                radius: isFocused ? shadowRadius : 0
            )
            .animation(AnimationSystem.focusAnimation(value: isFocused), value: isFocused)
    }
}

struct CardHoverModifier: ViewModifier {
    let isHovered: Bool
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isHovered ? 1.05 : 1.0)
            .brightness(isHovered ? 0.1 : 0)
            .animation(AnimationSystem.cardHoverAnimation(), value: isHovered)
    }
}

struct PulseModifier: ViewModifier {
    @State private var isPulsing = false
    let color: Color
    let intensity: Double
    
    init(color: Color = .white, intensity: Double = 0.3) {
        self.color = color
        self.intensity = intensity
    }
    
    func body(content: Content) -> some View {
        content
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.md)
                    .stroke(color.opacity(isPulsing ? intensity : 0), lineWidth: 2)
                    .scaleEffect(isPulsing ? 1.1 : 1.0)
                    .animation(AnimationSystem.pulseAnimation(), value: isPulsing)
            )
            .onAppear {
                isPulsing = true
            }
    }
}

struct ShimmerModifier: ViewModifier {
    @State private var phase: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .overlay(
                LinearGradient(
                    colors: [
                        Color.clear,
                        Color.white.opacity(0.3),
                        Color.clear
                    ],
                    startPoint: .leading,
                    endPoint: .trailing
                )
                .rotationEffect(.degrees(30))
                .offset(x: phase)
                .animation(
                    .linear(duration: 1.5).repeatForever(autoreverses: false),
                    value: phase
                )
            )
            .onAppear {
                phase = 300
            }
    }
}

struct ParallaxModifier: ViewModifier {
    let offset: CGFloat
    
    func body(content: Content) -> some View {
        content
            .offset(y: offset * 0.5)
    }
}

// MARK: - View Extensions

extension View {
    func focusAnimation(
        isFocused: Bool,
        scale: CGFloat = 1.1,
        shadowRadius: CGFloat = 20
    ) -> some View {
        self.modifier(FocusAnimationModifier(
            isFocused: isFocused,
            scale: scale,
            shadowRadius: shadowRadius
        ))
    }
    
    func cardHover(isHovered: Bool) -> some View {
        self.modifier(CardHoverModifier(isHovered: isHovered))
    }
    
    func pulse(color: Color = .white, intensity: Double = 0.3) -> some View {
        self.modifier(PulseModifier(color: color, intensity: intensity))
    }
    
    func shimmer() -> some View {
        self.modifier(ShimmerModifier())
    }
    
    func parallax(offset: CGFloat) -> some View {
        self.modifier(ParallaxModifier(offset: offset))
    }
    
    func slideTransition(from edge: Edge) -> some View {
        self.transition(.move(edge: edge).combined(with: .opacity))
    }
    
    func scaleTransition(scale: CGFloat = 0.8) -> some View {
        self.transition(.scale(scale: scale).combined(with: .opacity))
    }
    
    func modalTransition() -> some View {
        self.transition(AnimationSystem.modalPresentation)
    }
}

// MARK: - Loading Animation Views

struct LoadingSpinner: View {
    @State private var isRotating = false
    
    var body: some View {
        Image(systemName: "arrow.2.circlepath")
            .font(.system(size: 24, weight: .medium))
            .foregroundColor(DesignSystem.Colors.textSecondary)
            .rotationEffect(.degrees(isRotating ? 360 : 0))
            .animation(AnimationSystem.rotationAnimation(), value: isRotating)
            .onAppear {
                isRotating = true
            }
    }
}

struct PulsingDots: View {
    @State private var animationPhase = 0
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(0..<3) { index in
                Circle()
                    .fill(DesignSystem.Colors.textSecondary)
                    .frame(width: 8, height: 8)
                    .scaleEffect(animationPhase == index ? 1.5 : 1.0)
                    .animation(
                        .easeInOut(duration: 0.6).repeatForever(),
                        value: animationPhase
                    )
            }
        }
        .onAppear {
            Timer.scheduledTimer(withTimeInterval: 0.2, repeats: true) { _ in
                animationPhase = (animationPhase + 1) % 3
            }
        }
    }
}

struct BreathingGlow: View {
    @State private var isGlowing = false
    let color: Color
    
    init(color: Color = .blue) {
        self.color = color
    }
    
    var body: some View {
        Circle()
            .fill(color.opacity(isGlowing ? 0.3 : 0.1))
            .scaleEffect(isGlowing ? 1.2 : 1.0)
            .animation(AnimationSystem.breathingAnimation(), value: isGlowing)
            .onAppear {
                isGlowing = true
            }
    }
}

// MARK: - Transition Helpers

struct ConditionalTransition<Content: View>: View {
    let content: Content
    let condition: Bool
    let transition: AnyTransition
    
    init(
        condition: Bool,
        transition: AnyTransition,
        @ViewBuilder content: () -> Content
    ) {
        self.condition = condition
        self.transition = transition
        self.content = content()
    }
    
    var body: some View {
        if condition {
            content
                .transition(transition)
        } else {
            content
        }
    }
}

struct AnimatedContainer<Content: View>: View {
    let content: Content
    let isVisible: Bool
    let animation: Animation
    
    init(
        isVisible: Bool,
        animation: Animation = DesignSystem.Animations.medium,
        @ViewBuilder content: () -> Content
    ) {
        self.isVisible = isVisible
        self.animation = animation
        self.content = content()
    }
    
    var body: some View {
        if isVisible {
            content
                .transition(.opacity.combined(with: .scale))
                .animation(animation, value: isVisible)
        }
    }
}
