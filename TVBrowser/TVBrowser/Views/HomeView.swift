//
//  HomeView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI

struct HomeView: View {
    @StateObject private var viewModel = HomeViewModel()
    @Environment(\.navigationManager) private var navigationManager
    @Environment(\.focusManager) private var focusManager
    @State private var selectedMovie: Movie?
    @State private var showingMovieDetail = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 40) {
                    // Header
                    headerView
                    
                    if viewModel.isLoading {
                        loadingView
                    } else if let errorMessage = viewModel.errorMessage {
                        errorView(errorMessage)
                    } else {
                        // Featured Movies Section
                        if !viewModel.recommendations.isEmpty {
                            movieSection(
                                title: "推荐影片",
                                movies: Array(viewModel.recommendations.prefix(10))
                            )
                        }
                        
                        // Categories Section
                        if !viewModel.categories.isEmpty {
                            categoriesSection
                        }
                        
                        // Recent Movies Section
                        if viewModel.recommendations.count > 10 {
                            movieSection(
                                title: "最新上映",
                                movies: Array(viewModel.recommendations.dropFirst(10).prefix(10))
                            )
                        }
                    }
                }
                .tvSafePadding()
                .padding(.vertical, DesignSystem.Spacing.tvSectionSpacing)
            }
            .background(DesignSystem.Colors.gradientPrimary.ignoresSafeArea())
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingMovieDetail) {
            if let movie = selectedMovie {
                MovieDetailView(movie: movie)
            }
        }
        .onAppear {
            if viewModel.recommendations.isEmpty {
                viewModel.loadData()
            }
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 8) {
                Text("LibreTV")
                    .font(.system(size: 48, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                
                Text("发现精彩影视内容")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            Button(action: {
                viewModel.refresh()
            }) {
                Image(systemName: "arrow.clockwise")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 60, height: 60)
                    .background(Color.white.opacity(0.1))
                    .clipShape(Circle())
            }
            .buttonStyle(TVButtonStyle())
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(2.0)
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
            
            Text("加载中...")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: 400)
    }
    
    // MARK: - Error View
    
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.red)
            
            Text("加载失败")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            Text(message)
                .font(.system(size: 20))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
            
            Button("重试") {
                viewModel.refresh()
            }
            .buttonStyle(TVButtonStyle())
            .font(.system(size: 20, weight: .medium))
        }
        .frame(maxWidth: .infinity, maxHeight: 400)
    }
    
    // MARK: - Movie Section
    
    private func movieSection(title: String, movies: [Movie]) -> some View {
        VStack(alignment: .leading, spacing: 20) {
            Text(title)
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(.white)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 30) {
                    ForEach(Array(movies.enumerated()), id: \.element.id) { index, movie in
                        PremiumMovieCard(
                            movie: movie,
                            index: index,
                            section: .home
                        ) {
                            navigationManager.presentMovieDetail(for: movie)
                        }
                    }
                }
                .padding(.horizontal, 60)
            }
        }
    }
    
    // MARK: - Categories Section
    
    private var categoriesSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("分类")
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(.white)
            
            HStack(spacing: DesignSystem.Spacing.tvCardSpacing) {
                ForEach(Array(viewModel.categories.enumerated()), id: \.element.id) { index, category in
                    PremiumCategoryCard(
                        category: category,
                        index: index
                    ) {
                        navigationManager.presentCategoryView(for: category)
                    }
                }
                Spacer()
            }
        }
    }
}

// MARK: - Supporting Views

struct MovieCardView: View {
    let movie: Movie
    let onTap: () -> Void
    @State private var isFocused = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Poster
                AsyncImage(url: URL(string: movie.poster ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(2/3, contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .aspectRatio(2/3, contentMode: .fill)
                        .overlay(
                            Image(systemName: "photo")
                                .font(.system(size: 40))
                                .foregroundColor(.gray)
                        )
                }
                .frame(width: 200, height: 300)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .scaleEffect(isFocused ? 1.1 : 1.0)
//                .shadow(color: .black.opacity(0.9), radius: isFocused ? 20 : 10)
                
                // Title and Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(movie.title)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .lineLimit(2)
                    
                    Text("\(movie.displayYear) • \(movie.displayType)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.gray)
                        .lineLimit(1)
                }
                .frame(width: 200, alignment: .leading)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isFocused ? 1.1 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isFocused)
    }
}

struct CategoryCardView: View {
    let category: Category
    @State private var isFocused = false
    
    var body: some View {
        Button(action: {
            // TODO: Navigate to category view
        }) {
            Text(category.name)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 150, height: 80)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.opacity(isFocused ? 0.8 : 0.6))
                )
                .scaleEffect(isFocused ? 1.05 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isFocused ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isFocused)
    }
}

// MARK: - Custom Button Style

struct TVButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Enhanced Card Views

struct EnhancedMovieCardView: View {
    let movie: Movie
    let index: Int
    let section: FocusSection
    let onTap: () -> Void

    @Environment(\.focusManager) private var focusManager
    @State private var isFocused = false

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Poster
                AsyncImage(url: URL(string: movie.poster ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(2/3, contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .aspectRatio(2/3, contentMode: .fill)
                        .overlay(
                            Image(systemName: "photo")
                                .font(.system(size: 40))
                                .foregroundColor(.gray)
                        )
                }
                .frame(width: 200, height: 300)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .scaleEffect(isFocused ? 1.1 : 1.0)
                .shadow(color: .black.opacity(0.5), radius: isFocused ? 20 : 10)

                // Title and Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(movie.title)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .lineLimit(2)

                    Text("\(movie.displayYear) • \(movie.displayType)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.gray)
                        .lineLimit(1)
                }
                .frame(width: 200, alignment: .leading)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isFocused ? 1.1 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isFocused)
        .onReceive(focusManager.$currentFocusedItem) { focusedItem in
            isFocused = focusedItem?.type == .movieCard &&
                       focusedItem?.index == index &&
                       focusedItem?.section == section
        }
    }
}

struct EnhancedCategoryCardView: View {
    let category: Category
    let index: Int

    @Environment(\.focusManager) private var focusManager
    @State private var isFocused = false

    var body: some View {
        Button(action: {
            // TODO: Navigate to category view
        }) {
            Text(category.name)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 150, height: 80)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .foregroundColor(Color.blue.opacity(isFocused ? 0.8 : 0.6))
                )
                .scaleEffect(isFocused ? 1.05 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isFocused ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isFocused)
        .onReceive(focusManager.$currentFocusedItem) { focusedItem in
            isFocused = focusedItem?.type == .categoryCard &&
                       focusedItem?.index == index &&
                       focusedItem?.section == .home
        }
    }
}

#Preview {
    HomeView()
}
