//
//  SettingsView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI

struct SettingsView: View {
    @StateObject private var settings = SettingsManager.shared
    @FocusState private var focusedItem: SettingsFocusableItem?
    
    enum SettingsFocusableItem: Hashable {
        case adultContent
        case aggregatedSearch
        case autoPlay
        case adFiltering
        case resetSettings
        case closeButton
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.black,
                        Color.gray.opacity(0.3)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    headerView
                    
                    // Settings Content
                    ScrollView {
                        VStack(spacing: 40) {
                            contentFilteringSection
                            searchSettingsSection
                            playbackSettingsSection
                            aboutSection
                            resetSection
                        }
                        .padding(.horizontal, 80)
                        .padding(.vertical, 40)
                    }
                }
            }
        }
        .preferredColorScheme(.dark)
        .onAppear {
            focusedItem = .adultContent
        }
    }
    
    // MARK: - Header
    
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 8) {
                Text("设置")
                    .font(.system(size: 48, weight: .bold))
                    .foregroundColor(.white)
                
                Text("配置应用偏好设置")
                    .font(.system(size: 20))
                    .foregroundColor(.gray)
            }
            
            Spacer()
        }
        .padding(.horizontal, 80)
        .padding(.top, 60)
        .padding(.bottom, 20)
    }
    
    // MARK: - Content Filtering Section
    
    private var contentFilteringSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("内容过滤")
                .font(.system(size: 28, weight: .semibold))
                .foregroundColor(.white)
            
            SettingsToggleRow(
                title: "成人内容",
                subtitle: "启用后将在搜索结果中包含成人内容源",
                isOn: $settings.adultContentEnabled,
                isFocused: focusedItem == .adultContent
            )
            .focused($focusedItem, equals: .adultContent)
        }
    }
    
    // MARK: - Search Settings Section
    
    private var searchSettingsSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("搜索设置")
                .font(.system(size: 28, weight: .semibold))
                .foregroundColor(.white)
        }
    }
    
    // MARK: - Playback Settings Section
    
    private var playbackSettingsSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("播放设置")
                .font(.system(size: 28, weight: .semibold))
                .foregroundColor(.white)
            
            VStack(spacing: 15) {
                SettingsToggleRow(
                    title: "自动播放",
                    subtitle: "视频加载完成后自动开始播放",
                    isOn: $settings.autoPlayEnabled,
                    isFocused: focusedItem == .autoPlay
                )
                .focused($focusedItem, equals: .autoPlay)
            }
        }
    }
    
    // MARK: - About Section
    
    private var aboutSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("关于")
                .font(.system(size: 28, weight: .semibold))
                .foregroundColor(.white)
            
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Text("应用版本")
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                    Spacer()
                    Text("1.0.0")
                        .font(.system(size: 20))
                        .foregroundColor(.gray)
                }
                
                HStack {
                    Text("构建版本")
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                    Spacer()
                    Text("2025.07.24")
                        .font(.system(size: 20))
                        .foregroundColor(.gray)
                }
            }
            .padding(.vertical, 20)
            .padding(.horizontal, 30)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(15)
        }
    }
    
    // MARK: - Reset Section
    
    private var resetSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("重置")
                .font(.system(size: 28, weight: .semibold))
                .foregroundColor(.white)
            
            Button("恢复默认设置") {
                settings.resetToDefaults()
            }
            .font(.system(size: 20, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 30)
            .padding(.vertical, 15)
            .background(Color.red.opacity(0.7))
            .cornerRadius(10)
            .focused($focusedItem, equals: .resetSettings)
        }
    }
}

// MARK: - Settings Toggle Row

struct SettingsToggleRow: View {
    let title: String
    let subtitle: String
    @Binding var isOn: Bool
    let isFocused: Bool
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                
                Text(subtitle)
                    .font(.system(size: 18))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .toggleStyle(CustomToggleStyle(isFocused: isFocused))
        }
        .padding(.vertical, 20)
        .padding(.horizontal, 30)
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(isFocused ? Color.blue.opacity(0.3) : Color.gray.opacity(0.2))
                .overlay(
                    RoundedRectangle(cornerRadius: 15)
                        .stroke(isFocused ? Color.blue : Color.clear, lineWidth: 3)
                )
        )
    }
}

// MARK: - Custom Toggle Style

struct CustomToggleStyle: ToggleStyle {
    let isFocused: Bool
    
    func makeBody(configuration: Configuration) -> some View {
        HStack {
            configuration.label
            
            RoundedRectangle(cornerRadius: 20)
                .fill(configuration.isOn ? Color.blue : Color.gray.opacity(0.5))
                .frame(width: 60, height: 30)
                .overlay(
                    Circle()
                        .fill(Color.white)
                        .frame(width: 26, height: 26)
                        .offset(x: configuration.isOn ? 15 : -15)
                        .animation(.easeInOut(duration: 0.2), value: configuration.isOn)
                )
                .onTapGesture {
                    configuration.isOn.toggle()
                }
                .scaleEffect(isFocused ? 1.1 : 1.0)
                .animation(.easeInOut(duration: 0.2), value: isFocused)
        }
    }
}

#Preview {
    SettingsView()
}
