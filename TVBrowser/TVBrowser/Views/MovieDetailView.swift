//
//  MovieDetailView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI

struct MovieDetailView: View {
    let movie: Movie
    @StateObject private var viewModel = MovieDetailViewModel()
    @State private var selectedEpisode: Episode?
    @State private var showingVideoPlayer = false
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 30) {
                    // Header with close button
                    headerView
                    
                    if viewModel.isLoading {
                        loadingView
                    } else if let errorMessage = viewModel.errorMessage {
                        errorView(errorMessage)
                    } else {
                        // Movie Info Section
                        movieInfoSection
                        
                        // Episodes Section
                        if let detailMovie = viewModel.movie, detailMovie.hasEpisodes {
                            episodesSection(detailMovie.episodes ?? [])
                        } else if !movie.hasEpisodes {
                            // Show play button for single movies
                            playButtonSection
                        }
                    }
                }
                .padding(.horizontal, 60)
                .padding(.vertical, 40)
            }
            .background(
                LinearGradient(
                    colors: [
                        Color.black,
                        Color.black.opacity(0.95),
                        Color.black
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            )
            .navigationBarHidden(true)
        }
        .fullScreenCover(isPresented: $showingVideoPlayer) {
            if let episode = selectedEpisode {
                VideoPlayerView(episode: episode, movie: viewModel.movie ?? movie)
            } else if let url = movie.firstEpisodeURL {
                VideoPlayerView(
                    episode: Episode(episode: 1, title: movie.title, url: url),
                    movie: viewModel.movie ?? movie
                )
            }
        }
        .onAppear {
            if viewModel.movie == nil {
                viewModel.loadMovieDetail(id: String(movie.id), source: movie.source)
            }
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        HStack {
            Button(action: { dismiss() }) {
                Image(systemName: "xmark")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(Color.black.opacity(0.5))
                    .clipShape(Circle())
            }
            .buttonStyle(TVButtonStyle())
            
            Spacer()
            
            Text("影片详情")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            Spacer()
            
            // Placeholder for symmetry
            Color.clear
                .frame(width: 50, height: 50)
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(2.0)
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
            
            Text("加载详情中...")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: 400)
    }
    
    // MARK: - Error View
    
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.red)
            
            Text("加载失败")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            Text(message)
                .font(.system(size: 20))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
            
            Button("重试") {
                viewModel.refresh(id: String(movie.id), source: movie.source)
            }
            .buttonStyle(TVButtonStyle())
            .font(.system(size: 20, weight: .medium))
        }
        .frame(maxWidth: .infinity, maxHeight: 400)
    }
    
    // MARK: - Movie Info Section
    
    private var movieInfoSection: some View {
        let displayMovie = viewModel.movie ?? movie
        
        return HStack(alignment: .top, spacing: 40) {
            // Poster
            AsyncImage(url: URL(string: displayMovie.poster ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(2/3, contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .aspectRatio(2/3, contentMode: .fill)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                    )
            }
            .frame(width: 300, height: 450)
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(color: .black.opacity(0.5), radius: 20)
            
            // Movie Details
            VStack(alignment: .leading, spacing: 20) {
                // Title
                Text(displayMovie.title)
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.white)
                    .lineLimit(3)
                
                // Basic Info
                VStack(alignment: .leading, spacing: 8) {
                    InfoRow(label: "年份", value: displayMovie.displayYear)
                    InfoRow(label: "地区", value: displayMovie.displayArea)
                    InfoRow(label: "类型", value: displayMovie.displayType)
                    if let remarks = displayMovie.remarks, !remarks.isEmpty {
                        InfoRow(label: "状态", value: remarks)
                    }
                    InfoRow(label: "来源", value: displayMovie.sourceName)
                }
                
                // Director and Actor
                if let director = displayMovie.director, !director.isEmpty {
                    InfoRow(label: "导演", value: director)
                }
                if let actor = displayMovie.actor, !actor.isEmpty {
                    InfoRow(label: "主演", value: actor)
                }
                
                // Description
                if let description = displayMovie.description, !description.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("简介")
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.white)
                        
                        Text(description)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.gray)
                            .lineLimit(6)
                    }
                }
                
                Spacer()
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
    
    // MARK: - Episodes Section
    
    private func episodesSection(_ episodes: [Episode]) -> some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("选集播放")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: DesignSystem.Spacing.lg), count: 8), spacing: DesignSystem.Spacing.lg) {
                ForEach(Array(episodes.enumerated()), id: \.element.id) { index, episode in
                    EpisodeButton(episode: episode, index: index) {
                        selectedEpisode = episode
                        showingVideoPlayer = true
                    }
                }
            }
        }
    }
    
    // MARK: - Play Button Section
    
    private var playButtonSection: some View {
        HStack {
            Spacer()
            
            Button(action: {
                showingVideoPlayer = true
            }) {
                HStack(spacing: 15) {
                    Image(systemName: "play.fill")
                        .font(.system(size: 24, weight: .bold))
                    
                    Text("立即播放")
                        .font(.system(size: 24, weight: .bold))
                }
                .foregroundColor(.black)
                .padding(.horizontal, 40)
                .padding(.vertical, 15)
                .background(Color.white)
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            .buttonStyle(TVButtonStyle())
            
            Spacer()
        }
    }
}

// MARK: - Supporting Views

struct InfoRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 10) {
            Text("\(label):")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.gray)
                .frame(width: 60, alignment: .leading)
            
            Text(value)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .lineLimit(3)
            
            Spacer()
        }
    }
}

struct EpisodeButton: View {
    let episode: Episode
    let index: Int
    let action: () -> Void

    @Environment(\.focusManager) private var focusManager
    @State private var isFocused = false

    var body: some View {
        Button(action: action) {
            Text(episode.displayTitle)
                .font(DesignSystem.Typography.labelLarge)
                .foregroundColor(isFocused ? DesignSystem.Colors.background : DesignSystem.Colors.textPrimary)
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.vertical, DesignSystem.Spacing.md)
                .background(
                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.sm)
                        .fill(isFocused ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.surface)
                        .overlay(
                            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.sm)
                                .stroke(
                                    isFocused ? DesignSystem.Colors.accent : DesignSystem.Colors.surface,
                                    lineWidth: isFocused ? 2 : 1
                                )
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
        .focusAnimation(
            isFocused: isFocused,
            scale: DesignSystem.Sizes.focusScaleSmall
        )
        .onReceive(focusManager.$currentFocusedItem) { focusedItem in
            withAnimation(DesignSystem.Animations.focus) {
                isFocused = focusedItem?.type == .episodeButton &&
                           focusedItem?.index == index &&
                           focusedItem?.section == .movieDetail
            }
        }
    }
}

#Preview {
    MovieDetailView(movie: Movie.sample)
}
