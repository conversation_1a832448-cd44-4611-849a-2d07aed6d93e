//
//  SearchView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI

struct SearchView: View {
    @StateObject private var viewModel = SearchViewModel()
    @State private var selectedMovie: Movie?
    @State private var showingMovieDetail = false
    @FocusState private var focusedItem: FocusableSearchItem?
    
    enum FocusableSearchItem: Hashable {
        case searchField
        case sourceButton(String)
        case searchResult(Int)
        case clearButton
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header with Search
                headerView
                
                // Source Selection
                sourceSelectionView
                
                // Search Results
                if viewModel.isSearching {
                    loadingView
                } else if let errorMessage = viewModel.errorMessage {
                    errorView(errorMessage)
                } else if !viewModel.searchResults.isEmpty {
                    searchResultsView
                } else if !viewModel.searchText.isEmpty {
                    noResultsView
                } else {
                    searchPromptView
                }
                
                Spacer()
            }
            .padding(.horizontal, 60)
            .padding(.vertical, 40)
            .background(
                // Enhanced background
                ZStack {
                    Color.black.ignoresSafeArea()
                    
                    LinearGradient(
                        colors: [
                            Color.black,
                            Color.black.opacity(0.98),
                            Color.black.opacity(0.95),
                            Color.black
                        ],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .ignoresSafeArea()
                }
            )
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingMovieDetail) {
            if let movie = selectedMovie {
                MovieDetailView(movie: movie)
            }
        }
        .onAppear {
            // Set initial focus to search field
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                focusedItem = .searchField
            }
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        VStack(spacing: 20) {
            HStack {
                Text("搜索影片")
                    .font(.system(size: 40, weight: .bold))
                    .foregroundColor(.white)
                
                Spacer()
                
                if !viewModel.searchText.isEmpty {
                    Button("清除") {
                        viewModel.clearSearch()
                        focusedItem = .searchField
                    }
                    .buttonStyle(EnhancedButtonStyle(style: .secondary))
                    .font(.system(size: 18, weight: .medium))
                    .focused($focusedItem, equals: .clearButton)
                    .scaleEffect(focusedItem == .clearButton ? 1.05 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.8), value: focusedItem == .clearButton)
                }
            }
            
            // Enhanced Search Field
            HStack {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 24))
                    .foregroundColor(.gray)
                
                TextField("输入影片名称...", text: $viewModel.searchText)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .focused($focusedItem, equals: .searchField)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onSubmit {
                        if !viewModel.searchText.isEmpty {
                            viewModel.performSearch(query: viewModel.searchText)
                        }
                    }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 15)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(focusedItem == .searchField ? 0.15 : 0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                focusedItem == .searchField ? Color.white.opacity(0.8) : Color.white.opacity(0.3),
                                lineWidth: focusedItem == .searchField ? 2 : 1
                            )
                    )
            )
            .scaleEffect(focusedItem == .searchField ? 1.02 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: focusedItem == .searchField)
        }
    }
    
    // MARK: - Source Selection View
    
    private var sourceSelectionView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("数据源")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 15) {
                    ForEach(viewModel.availableSources, id: \.0) { source in
                        FocusableSourceButton(
                            title: source.1,
                            sourceKey: source.0,
                            isSelected: viewModel.selectedSource == source.0,
                            isFocused: focusedItem == .sourceButton(source.0)
                        ) {
                            viewModel.changeSource(source.0)
                        }
                        .focused($focusedItem, equals: .sourceButton(source.0))
                    }
                }
                .padding(.horizontal, 60)
            }
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 30) {
            // Enhanced loading animation
            ZStack {
                Circle()
                    .stroke(Color.white.opacity(0.2), lineWidth: 4)
                    .frame(width: 80, height: 80)
                
                Circle()
                    .trim(from: 0, to: 0.3)
                    .stroke(Color.white, lineWidth: 4)
                    .frame(width: 80, height: 80)
                    .rotationEffect(.degrees(-90))
                    .rotationEffect(.degrees(viewModel.isSearching ? 360 : 0))
                    .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: viewModel.isSearching)
            }
            
            Text("搜索中...")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.gray)
        }
        .frame(maxHeight: 300)
    }
    
    // MARK: - Error View
    
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 30) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 60))
                .foregroundColor(.red)
            
            Text("搜索失败")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            Text(message)
                .font(.system(size: 20))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .lineLimit(3)
            
            Button("重试") {
                viewModel.performSearch(query: viewModel.searchText)
            }
            .buttonStyle(EnhancedButtonStyle())
            .font(.system(size: 20, weight: .medium))
        }
        .frame(maxHeight: 300)
    }
    
    // MARK: - Search Results View
    
    private var searchResultsView: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("搜索结果 (\(viewModel.searchResults.count))")
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(.white)
            
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 30), count: 5), spacing: 30) {
                    ForEach(Array(viewModel.searchResults.enumerated()), id: \.element.id) { index, movie in
                        FocusableSearchResultCard(
                            movie: movie,
                            index: index,
                            isFocused: focusedItem == .searchResult(index)
                        ) {
                            print("🎬 Search result tapped: \(movie.title)")
                            selectedMovie = movie
                            showingMovieDetail = true
                        }
                        .focused($focusedItem, equals: .searchResult(index))
                    }
                }
                .padding(.horizontal, 60)
            }
        }
    }
    
    // MARK: - No Results View
    
    private var noResultsView: some View {
        VStack(spacing: 30) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("未找到相关影片")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            Text("尝试使用不同的关键词或更换数据源")
                .font(.system(size: 20))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .frame(maxHeight: 300)
    }
    
    // MARK: - Search Prompt View
    
    private var searchPromptView: some View {
        VStack(spacing: 30) {
            Image(systemName: "tv")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            Text("开始搜索")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            Text("输入影片名称来搜索您想观看的内容")
                .font(.system(size: 20))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .frame(maxHeight: 300)
    }
}

// MARK: - Enhanced Supporting Views

struct FocusableSourceButton: View {
    let title: String
    let sourceKey: String
    let isSelected: Bool
    let isFocused: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(isSelected ? .black : .white)
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(isSelected ? Color.white : Color.white.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(
                                    isFocused ? Color.white.opacity(0.8) : Color.white.opacity(0.3),
                                    lineWidth: isFocused ? 2 : 1
                                )
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isFocused ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isFocused)
    }
}

struct FocusableSearchResultCard: View {
    let movie: Movie
    let index: Int
    let isFocused: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                // Poster with enhanced focus effects
                AsyncImage(url: URL(string: movie.poster ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(2/3, contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .aspectRatio(2/3, contentMode: .fill)
                        .overlay(
                            VStack(spacing: 8) {
                                Image(systemName: "photo")
                                    .font(.system(size: 30))
                                    .foregroundColor(.gray)
                                
                                // Loading dots
                                HStack(spacing: 4) {
                                    ForEach(0..<3) { dotIndex in
                                        Circle()
                                            .fill(Color.gray)
                                            .frame(width: 6, height: 6)
                                            .scaleEffect(dotIndex == 0 ? 1.2 : 1.0)
                                            .animation(.easeInOut(duration: 0.6).repeatForever().delay(Double(dotIndex) * 0.2), value: dotIndex)
                                    }
                                }
                            }
                        )
                }
                .frame(width: 160, height: 240)
                .clipShape(RoundedRectangle(cornerRadius: 8))
                .overlay(
                    // Focus border
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.white.opacity(isFocused ? 0.8 : 0), lineWidth: 3)
                )
                .shadow(
                    color: isFocused ? Color.white.opacity(0.3) : Color.black.opacity(0.3),
                    radius: isFocused ? 15 : 5,
                    x: 0,
                    y: isFocused ? 6 : 2
                )
                
                // Title and Info
                VStack(alignment: .leading, spacing: 2) {
                    Text(movie.title)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                    
                    Text(movie.displayYear)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.gray)
                        .lineLimit(1)
                }
                .frame(width: 160, alignment: .leading)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isFocused ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isFocused)
    }
}

// MARK: - Enhanced Button Style

struct EnhancedButtonStyle: ButtonStyle {
    enum Style {
        case primary, secondary
    }
    
    let style: Style
    
    init(style: Style = .primary) {
        self.style = style
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(style == .primary ? .black : .white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(style == .primary ? Color.white : Color.white.opacity(0.2))
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Legacy Supporting Views (keeping for compatibility)

struct SourceButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    @State private var isFocused = false
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(isSelected ? .black : .white)
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .foregroundColor(isSelected ? Color.white : Color.white.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
                .scaleEffect(isFocused ? 1.05 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isFocused ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isFocused)
    }
}

struct SearchResultCardView: View {
    let movie: Movie
    let onTap: () -> Void
    @State private var isFocused = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                // Poster
                AsyncImage(url: URL(string: movie.poster ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(2/3, contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .aspectRatio(2/3, contentMode: .fill)
                        .overlay(
                            Image(systemName: "photo")
                                .font(.system(size: 30))
                                .foregroundColor(.gray)
                        )
                }
                .frame(width: 160, height: 240)
                .clipShape(RoundedRectangle(cornerRadius: 8))
                .scaleEffect(isFocused ? 1.05 : 1.0)
                .shadow(color: .black.opacity(0.5), radius: isFocused ? 15 : 8)
                
                // Title and Info
                VStack(alignment: .leading, spacing: 2) {
                    Text(movie.title)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white)
                        .lineLimit(2)
                    
                    Text(movie.displayYear)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.gray)
                        .lineLimit(1)
                }
                .frame(width: 160, alignment: .leading)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isFocused ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isFocused)
    }
}

#Preview {
    SearchView()
}
