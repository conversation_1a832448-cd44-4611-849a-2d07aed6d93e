//
//  CategoryView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI
import Combine

// MARK: - Category View

struct CategoryView: View {
    let category: Category
    
    @StateObject private var viewModel: CategoryViewModel
    @Environment(\.navigationManager) private var navigationManager
    @Environment(\.focusManager) private var focusManager
    
    init(category: Category) {
        self.category = category
        self._viewModel = StateObject(wrappedValue: CategoryViewModel(category: category))
    }
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: DesignSystem.Spacing.tvSectionSpacing) {
                // Header
                headerView
                
                // Movies Grid
                if viewModel.isLoading && viewModel.movies.isEmpty {
                    loadingView
                } else if viewModel.movies.isEmpty {
                    emptyStateView
                } else {
                    moviesGridView
                }
                
                // Load More Button
                if viewModel.hasMorePages && !viewModel.isLoading {
                    loadMoreButton
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.tvSafeArea)
            .padding(.vertical, DesignSystem.Spacing.tvSectionSpacing)
        }
        .background(DesignSystem.Colors.gradientPrimary.ignoresSafeArea())
        .onAppear {
            viewModel.loadMovies()
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                Text(category.name)
                    .font(DesignSystem.Typography.displayMedium)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                if !viewModel.movies.isEmpty {
                    Text("共 \(viewModel.totalMovies) 部影片")
                        .font(DesignSystem.Typography.bodyLarge)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
            
            Spacer()
            
            // Source Selector
            sourceSelector
        }
    }
    
    private var sourceSelector: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            ForEach(["heimuer", "bfzy", "ruyi"], id: \.self) { source in
                Button(action: {
                    viewModel.changeSource(source)
                }) {
                    Text(getSourceName(source))
                        .font(DesignSystem.Typography.labelLarge)
                        .foregroundColor(
                            viewModel.currentSource == source ? 
                            DesignSystem.Colors.textPrimary : 
                            DesignSystem.Colors.textSecondary
                        )
                        .padding(.horizontal, DesignSystem.Spacing.md)
                        .padding(.vertical, DesignSystem.Spacing.sm)
                        .background(
                            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.sm)
                                .fill(
                                    viewModel.currentSource == source ? 
                                    DesignSystem.Colors.accent.opacity(0.2) : 
                                    Color.clear
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - Movies Grid View
    
    private var moviesGridView: some View {
        LazyVGrid(
            columns: Array(repeating: GridItem(.flexible(), spacing: DesignSystem.Spacing.tvCardSpacing), count: 6),
            spacing: DesignSystem.Spacing.tvCardSpacing
        ) {
            ForEach(Array(viewModel.movies.enumerated()), id: \.element.id) { index, movie in
                PremiumMovieCard(
                    movie: movie,
                    index: index,
                    section: .category
                ) {
                    navigationManager.presentMovieDetail(for: movie)
                }
            }
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: DesignSystem.Spacing.xl) {
            LoadingSpinner()
            
            Text("正在加载\(category.name)...")
                .font(DesignSystem.Typography.headlineMedium)
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, minHeight: 300)
    }
    
    // MARK: - Empty State View
    
    private var emptyStateView: some View {
        VStack(spacing: DesignSystem.Spacing.xl) {
            Image(systemName: "tv.slash")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            VStack(spacing: DesignSystem.Spacing.md) {
                Text("暂无\(category.name)")
                    .font(DesignSystem.Typography.displaySmall)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("请尝试更换数据源或稍后再试")
                    .font(DesignSystem.Typography.bodyLarge)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, minHeight: 400)
    }
    
    // MARK: - Load More Button
    
    private var loadMoreButton: some View {
        Button(action: {
            viewModel.loadMoreMovies()
        }) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                if viewModel.isLoadingMore {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.textPrimary))
                } else {
                    Image(systemName: "arrow.down.circle")
                        .font(.system(size: 20, weight: .medium))
                }
                
                Text(viewModel.isLoadingMore ? "加载中..." : "加载更多")
                    .font(DesignSystem.Typography.titleMedium)
            }
            .foregroundColor(DesignSystem.Colors.textPrimary)
            .padding(.horizontal, DesignSystem.Spacing.xl)
            .padding(.vertical, DesignSystem.Spacing.lg)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.md)
                    .fill(DesignSystem.Colors.surface)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(viewModel.isLoadingMore)
    }
    
    // MARK: - Helper Methods
    
    private func getSourceName(_ source: String) -> String {
        switch source {
        case "heimuer": return "黑木耳"
        case "bfzy": return "暴风资源"
        case "ruyi": return "如意资源"
        default: return source
        }
    }
}

// MARK: - Category View Model

class CategoryViewModel: ObservableObject {
    @Published var movies: [Movie] = []
    @Published var isLoading = false
    @Published var isLoadingMore = false
    @Published var hasMorePages = true
    @Published var currentSource = "heimuer"
    @Published var totalMovies = 0
    
    private let category: Category
    private var currentPage = 1
    private var cancellables = Set<AnyCancellable>()
    private let apiService = APIService.shared
    
    init(category: Category) {
        self.category = category
    }
    
    func loadMovies() {
        guard !isLoading else { return }
        
        isLoading = true
        currentPage = 1
        movies.removeAll()
        
        apiService.fetchMoviesByCategory(
            categoryType: category.type,
            page: currentPage,
            source: currentSource
        )
        .sink(
            receiveCompletion: { [weak self] (completion: Subscribers.Completion<APIError>) in
                DispatchQueue.main.async {
                    self?.isLoading = false
                }
                if case .failure(let error) = completion {
                    print("Failed to load movies: \(error)")
                }
            },
            receiveValue: { [weak self] (movies: [Movie]) in
                DispatchQueue.main.async {
                    self?.movies = movies
                    self?.totalMovies = movies.count
                    self?.hasMorePages = movies.count >= 20 // Assume more pages if we got a full page
                    self?.isLoading = false
                }
            }
        )
        .store(in: &cancellables)
    }
    
    func loadMoreMovies() {
        guard !isLoadingMore && hasMorePages else { return }
        
        isLoadingMore = true
        currentPage += 1
        
        apiService.fetchMoviesByCategory(
            categoryType: category.type,
            page: currentPage,
            source: currentSource
        )
        .sink(
            receiveCompletion: { [weak self] (completion: Subscribers.Completion<APIError>) in
                DispatchQueue.main.async {
                    self?.isLoadingMore = false
                }
                if case .failure(let error) = completion {
                    print("Failed to load more movies: \(error)")
                    self?.currentPage -= 1 // Revert page increment on error
                }
            },
            receiveValue: { [weak self] (newMovies: [Movie]) in
                DispatchQueue.main.async {
                    self?.movies.append(contentsOf: newMovies)
                    self?.totalMovies += newMovies.count
                    self?.hasMorePages = newMovies.count >= 20
                    self?.isLoadingMore = false
                }
            }
        )
        .store(in: &cancellables)
    }
    
    func changeSource(_ newSource: String) {
        guard newSource != currentSource else { return }
        currentSource = newSource
        loadMovies()
    }
}

// MARK: - Preview

#Preview {
    CategoryView(category: Category(id: 1, name: "电影", type: "1"))
}
