//
//  VideoPlayerView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI
import AVKit

struct VideoPlayerView: View {
    let episode: Episode
    let movie: Movie
    @StateObject private var videoService = VideoPlayerService.shared
    @Environment(\.dismiss) private var dismiss
    @Environment(\.navigationManager) private var navigationManager
    @Environment(\.focusManager) private var focusManager
    @State private var showControls = true
    @State private var controlsTimer: Timer?
    @State private var showSettings = false
    @State private var isControlsFocused = false
    
    var body: some View {
        ZStack {
            // Video Player Background
            Color.black.ignoresSafeArea()
            
            // Video Player
            if let player = videoService.player {
                VideoPlayer(player: player)
                    .ignoresSafeArea()
                    .onTapGesture {
                        toggleControls()
                    }
            } else {
                // Enhanced Loading State
                loadingStateView
            }
            
            // Custom Controls Overlay with enhanced design
            if showControls {
                VStack(spacing: 0) {
                    // Top Controls with gradient background
                    topControlsView
                    
                    Spacer()
                    
                    // Bottom Controls with enhanced design
                    bottomControlsView
                }
                .background(
                    // Enhanced gradient overlay
                    LinearGradient(
                        colors: [
                            Color.black.opacity(0.8),
                            Color.clear,
                            Color.clear,
                            Color.black.opacity(0.8)
                        ],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .ignoresSafeArea()
                )
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
            
            // Error overlay - only show if video player is not in loading state
            if videoService.hasError && !videoService.isLoading && videoService.player == nil {
                VStack(spacing: 24) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.red)
                    
                    Text("播放失败")
                        .font(.system(size: 26, weight: .bold))
                        .foregroundColor(.white)
                    
                    if let errorMessage = videoService.errorMessage {
                        Text(getSimplifiedErrorMessage(errorMessage))
                            .font(.system(size: 20, weight: .regular))
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .lineLimit(3)
                            .padding(.horizontal, 60)
                    }
                    
                    HStack(spacing: 30) {
                        if videoService.canRetry {
                            Button("重试") {
                                videoService.retryPlayback()
                            }
                            .buttonStyle(EnhancedButtonStyle(style: .primary))
                        }
                        
                        Button("返回") {
                            dismiss()
                        }
                        .buttonStyle(EnhancedButtonStyle(style: .secondary))
                    }
                    .padding(.top, 10)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black.opacity(0.9))
            }
        }
        .onAppear {
            print("🎬 VideoPlayerView appeared for episode: \(episode.displayTitle), URL: \(episode.url)")
            setupPlayer()
            startControlsTimer()
            setupFocus()
        }
        .onDisappear {
            videoService.cleanup()
            controlsTimer?.invalidate()
            saveWatchProgress()
        }
        .onReceive(NotificationCenter.default.publisher(for: .AVPlayerItemDidPlayToEndTime)) { _ in
            handleVideoEnd()
        }
        .onReceive(NotificationCenter.default.publisher(for: .videoPlayerTogglePlayPause)) { _ in
            videoService.togglePlayPause()
        }
        .onReceive(NotificationCenter.default.publisher(for: .videoPlayerDidFinish)) { _ in
            handleVideoEnd()
        }
    }
    
    // MARK: - Enhanced Loading State
    
    private var loadingStateView: some View {
        VStack(spacing: 40) {
            // Animated loading spinner with better design
            ZStack {
                Circle()
                    .stroke(Color.white.opacity(0.2), lineWidth: 6)
                    .frame(width: 100, height: 100)
                
                Circle()
                    .trim(from: 0, to: 0.3)
                    .stroke(Color.white, lineWidth: 6)
                    .frame(width: 100, height: 100)
                    .rotationEffect(.degrees(-90))
                    .rotationEffect(.degrees(videoService.isLoading ? 360 : 0))
                    .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: videoService.isLoading)
            }
            
            VStack(spacing: 20) {
                Text(videoService.isLoading ? "正在加载视频..." : "准备播放...")
                    .font(.system(size: 28, weight: .medium))
                    .foregroundColor(.white)
                
                Text("\(movie.title)")
                    .font(.system(size: 22, weight: .regular))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                
                if !episode.title.isEmpty {
                    Text(episode.displayTitle)
                        .font(.system(size: 20, weight: .regular))
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                }
            }
            
            // Simplified error handling
            if videoService.hasError {
                VStack(spacing: 24) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.red)
                    
                    Text("播放失败")
                        .font(.system(size: 26, weight: .bold))
                        .foregroundColor(.white)
                    
                    if let errorMessage = videoService.errorMessage {
                        Text(getSimplifiedErrorMessage(errorMessage))
                            .font(.system(size: 20, weight: .regular))
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .lineLimit(3)
                            .padding(.horizontal, 60)
                    }
                    
                    HStack(spacing: 30) {
                        if videoService.canRetry {
                            Button("重试") {
                                videoService.retryPlayback()
                            }
                            .buttonStyle(EnhancedButtonStyle(style: .primary))
                        }
                        
                        Button("返回") {
                            dismiss()
                        }
                        .buttonStyle(EnhancedButtonStyle(style: .secondary))
                    }
                    .padding(.top, 10)
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    

    
    // MARK: - Enhanced Top Controls
    
    private var topControlsView: some View {
        HStack(alignment: .top) {
            // Close Button with enhanced design
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "xmark")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(.ultraThinMaterial)
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )
                    )
            }
            .buttonStyle(TVButtonStyle())
            
            Spacer()
            
            // Enhanced Title Section
            VStack(spacing: 6) {
                Text(movie.title)
                    .font(.system(size: 22, weight: .bold))
                    .foregroundColor(.white)
                    .lineLimit(1)
                
                Text(episode.displayTitle)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .lineLimit(1)
                
                // Additional info
                if videoService.isLive {
                    HStack(spacing: 4) {
                        Circle()
                            .fill(.red)
                            .frame(width: 6, height: 6)
                        Text("直播")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.red)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(Color.red.opacity(0.2))
                    .clipShape(Capsule())
                }
            }
            
            Spacer()
            
            // Settings and Info Buttons
            HStack(spacing: 12) {
                // Quality indicator
                if let quality = videoService.currentQuality {
                    Text(quality.name)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.white.opacity(0.2))
                        .clipShape(Capsule())
                }
                
                // Settings Button
                Button(action: {
                    showSettings.toggle()
                }) {
                    Image(systemName: "gearshape.fill")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                )
                        )
                }
                .buttonStyle(TVButtonStyle())
            }
        }
        .padding(.horizontal, 50)
        .padding(.top, 50)
    }
    
    // MARK: - Enhanced Bottom Controls
    
    private var bottomControlsView: some View {
        VStack(spacing: 25) {
            // Enhanced Progress Bar
            EnhancedVideoProgressView()
                .padding(.horizontal, 50)
            
            // Playback Controls with better design
            HStack(spacing: 50) {
                // Previous Episode Button
                Button(action: {
                    // TODO: Implement previous episode
                }) {
                    Image(systemName: "backward.end.fill")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                }
                .buttonStyle(TVButtonStyle())
                
                // Rewind Button
                Button(action: {
                    videoService.seekBackward()
                }) {
                    Image(systemName: "gobackward.10")
                        .font(.system(size: 28, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 70, height: 70)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                }
                .buttonStyle(TVButtonStyle())
                
                // Enhanced Play/Pause Button
                Button(action: {
                    videoService.togglePlayPause()
                }) {
                    Image(systemName: videoService.isPlaying ? "pause.fill" : "play.fill")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.white)
                        .frame(width: 90, height: 90)
                        .background(
                            Circle()
                                .fill(
                                    LinearGradient(
                                        colors: [Color.white.opacity(0.3), Color.white.opacity(0.1)],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .overlay(
                                    Circle()
                                        .stroke(Color.white.opacity(0.4), lineWidth: 2)
                                )
                        )
                        .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
                }
                .buttonStyle(TVButtonStyle())
                .scaleEffect(isControlsFocused ? 1.1 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isControlsFocused)

                // Fast Forward Button
                Button(action: {
                    videoService.seekForward()
                }) {
                    Image(systemName: "goforward.10")
                        .font(.system(size: 28, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 70, height: 70)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                }
                .buttonStyle(TVButtonStyle())
                
                // Next Episode Button
                Button(action: {
                    // TODO: Implement next episode
                }) {
                    Image(systemName: "forward.end.fill")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                }
                .buttonStyle(TVButtonStyle())
            }
        }
        .padding(.horizontal, 50)
        .padding(.bottom, 50)
    }
    
    // MARK: - Helper Methods
    
    private func setupPlayer() {
        print("🎬 Setting up player for URL: \(episode.url)")
        // Check for saved progress
        let savedProgress = videoService.getWatchProgress(for: movie, episode: episode) ?? 0
        print("🎬 Saved progress: \(savedProgress)")
        videoService.setupPlayer(with: episode.url, startTime: savedProgress)
    }

    private func setupFocus() {
        // Set focus to play/pause button
        let playButtonFocus = FocusableItem(
            id: "play_pause_button",
            type: .controlButton,
            index: 0,
            section: .videoPlayer
        )
        focusManager.setFocus(to: playButtonFocus, in: .videoPlayer)
        isControlsFocused = true
    }

    private func saveWatchProgress() {
        videoService.saveWatchProgress(
            for: movie,
            episode: episode,
            currentTime: videoService.currentTime
        )
    }

    private func handleVideoEnd() {
        saveWatchProgress()

        // Check if there's a next episode
        if let episodes = movie.episodes,
           let currentIndex = episodes.firstIndex(where: { $0.id == episode.id }),
           currentIndex + 1 < episodes.count {
            // Auto-play next episode
            let nextEpisode = episodes[currentIndex + 1]
            navigationManager.currentEpisode = nextEpisode
            videoService.setupPlayer(with: nextEpisode.url)
        } else {
            // End of series, dismiss player
            navigationManager.dismissVideoPlayer()
        }
    }
    
    private func toggleControls() {
        withAnimation(.easeInOut(duration: 0.4)) {
            showControls.toggle()
        }
        
        if showControls {
            startControlsTimer()
        } else {
            controlsTimer?.invalidate()
        }
    }
    
    private func startControlsTimer() {
        controlsTimer?.invalidate()
        controlsTimer = Timer.scheduledTimer(withTimeInterval: 4.0, repeats: false) { _ in
            withAnimation(.easeInOut(duration: 0.4)) {
                showControls = false
            }
        }
    }

    private func getSimplifiedErrorMessage(_ originalMessage: String) -> String {
        // Simplify common error messages for better user experience
        if originalMessage.contains("网络") || originalMessage.contains("连接") {
            return "网络连接失败，请检查网络后重试"
        } else if originalMessage.contains("超时") {
            return "加载超时，请重试"
        } else if originalMessage.contains("无效") || originalMessage.contains("URL") {
            return "视频源无效，请尝试其他剧集"
        } else if originalMessage.contains("播放失败") {
            return "视频播放失败，请重试"
        } else {
            return "播放出现问题，请重试"
        }
    }
}

// MARK: - Enhanced Video Progress View

struct EnhancedVideoProgressView: View {
    @StateObject private var videoService = VideoPlayerService.shared
    @State private var isDragging = false
    @State private var dragProgress: Double = 0
    
    var body: some View {
        VStack(spacing: 12) {
            // Progress Bar with improved design
            HStack(spacing: 15) {
                Text(timeString(from: videoService.currentTime))
                    .font(.system(size: 14, weight: .medium, design: .monospaced))
                    .foregroundColor(.white)
                    .frame(width: 55, alignment: .leading)

                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // Background track
                        RoundedRectangle(cornerRadius: 3)
                            .fill(Color.white.opacity(0.3))
                            .frame(height: 6)

                        // Buffered progress
                        RoundedRectangle(cornerRadius: 3)
                            .fill(Color.white.opacity(0.5))
                            .frame(width: geometry.size.width * videoService.progress, height: 6)

                        // Current progress
                        RoundedRectangle(cornerRadius: 3)
                            .fill(
                                LinearGradient(
                                    colors: [Color.white, Color.white.opacity(0.8)],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: geometry.size.width * (isDragging ? dragProgress : videoService.progress), height: 6)
                        
                        // Progress thumb
                        Circle()
                            .fill(Color.white)
                            .frame(width: 12, height: 12)
                            .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                            .offset(x: geometry.size.width * (isDragging ? dragProgress : videoService.progress) - 6)
                    }
                }
                .frame(height: 12)

                Text(timeString(from: videoService.duration))
                    .font(.system(size: 14, weight: .medium, design: .monospaced))
                    .foregroundColor(.white)
                    .frame(width: 55, alignment: .trailing)
            }

            // Additional playback info
            HStack {
                // Playback speed indicator
                if videoService.playbackRate != 1.0 {
                    Text("\(String(format: "%.1f", videoService.playbackRate))x")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.white.opacity(0.2))
                        .clipShape(Capsule())
                }
                
                Spacer()
                
                // Live indicator
                if videoService.isLive {
                    HStack(spacing: 4) {
                        Circle()
                            .fill(.red)
                            .frame(width: 6, height: 6)
                        Text("直播")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.red)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 3)
                    .background(Color.red.opacity(0.2))
                    .clipShape(Capsule())
                }
            }
        }
    }
    
    private func timeString(from seconds: Double) -> String {
        guard !seconds.isNaN && !seconds.isInfinite else { return "00:00" }
        
        let totalSeconds = Int(seconds)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let secs = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, secs)
        } else {
            return String(format: "%02d:%02d", minutes, secs)
        }
    }
}

// MARK: - Modern Button Style

struct ModernButtonStyle: ButtonStyle {
    enum Style {
        case primary, secondary
    }
    
    let style: Style
    
    init(style: Style = .primary) {
        self.style = style
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(style == .primary ? .black : .white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(style == .primary ? Color.white : Color.white.opacity(0.2))
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}



#Preview {
    VideoPlayerView(
        episode: Episode(episode: 1, title: "Test Episode", url: "https://example.com/video.m3u8"),
        movie: Movie.sample
    )
}
