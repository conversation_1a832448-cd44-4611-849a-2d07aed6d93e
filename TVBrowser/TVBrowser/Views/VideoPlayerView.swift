//
//  VideoPlayerView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI
import AVKit

struct VideoPlayerView: View {
    let episode: Episode
    let movie: Movie
    @StateObject private var videoService = VideoPlayerService.shared
    @Environment(\.dismiss) private var dismiss
    @Environment(\.navigationManager) private var navigationManager
    @Environment(\.focusManager) private var focusManager
    @State private var showControls = true
    @State private var controlsTimer: Timer?
    @State private var showSettings = false
    
    var body: some View {
        ZStack {
            // Video Player
            if let player = videoService.player {
                VideoPlayer(player: player)
                    .ignoresSafeArea()
            } else {
                // Loading state
                ZStack {
                    Color.black.ignoresSafeArea()

                    VStack(spacing: 20) {
                        ProgressView()
                            .scaleEffect(2.0)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))

                        Text(videoService.isLoading ? "正在加载视频..." : "准备播放...")
                            .font(.system(size: 24, weight: .medium))
                            .foregroundColor(.white)

                        if videoService.hasError, let errorMessage = videoService.errorMessage {
                            Text(errorMessage)
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(.red)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 40)
                        }
                    }
                }
            }
            
            // Custom Controls Overlay
            if showControls {
                VStack {
                    // Top Controls
                    topControlsView
                    
                    Spacer()
                    
                    // Bottom Controls
                    bottomControlsView
                }
                .background(
                    LinearGradient(
                        colors: [
                            Color.black.opacity(0.7),
                            Color.clear,
                            Color.black.opacity(0.7)
                        ],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .ignoresSafeArea()
                )
                .transition(.opacity)
            }
        }
        .onAppear {
            print("🎬 VideoPlayerView appeared for episode: \(episode.displayTitle), URL: \(episode.url)")
            setupPlayer()
            startControlsTimer()
            setupFocus()
        }
        .onDisappear {
            videoService.cleanup()
            controlsTimer?.invalidate()
            saveWatchProgress()
        }
        .onReceive(NotificationCenter.default.publisher(for: .AVPlayerItemDidPlayToEndTime)) { _ in
            handleVideoEnd()
        }
        .onReceive(NotificationCenter.default.publisher(for: .videoPlayerTogglePlayPause)) { _ in
            videoService.togglePlayPause()
        }
        .onReceive(NotificationCenter.default.publisher(for: .videoPlayerDidFinish)) { _ in
            handleVideoEnd()
        }
    }
    
    // MARK: - Top Controls
    
    private var topControlsView: some View {
        HStack {
            // Close Button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "xmark")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(Color.black.opacity(0.5))
                    .clipShape(Circle())
            }
            .buttonStyle(TVButtonStyle())
            
            Spacer()
            
            // Title
            VStack(alignment: .center, spacing: 4) {
                Text(movie.title)
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
                    .lineLimit(1)
                
                Text(episode.displayTitle)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.gray)
                    .lineLimit(1)
            }
            
            Spacer()
            
            // Settings Button (placeholder)
            Button(action: {
                // TODO: Show settings
            }) {
                Image(systemName: "gearshape")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(Color.black.opacity(0.5))
                    .clipShape(Circle())
            }
            .buttonStyle(TVButtonStyle())
        }
        .padding(.horizontal, 60)
        .padding(.top, 40)
    }
    
    // MARK: - Bottom Controls
    
    private var bottomControlsView: some View {
        VStack(spacing: 20) {
            // Progress Bar
            EnhancedVideoProgressView()
            
            // Playback Controls
            HStack(spacing: 40) {
                // Rewind Button
                Button(action: {
                    videoService.seekBackward()
                }) {
                    Image(systemName: "gobackward.10")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                }
                .buttonStyle(TVButtonStyle())
                
                // Play/Pause Button
                Button(action: {
                    videoService.togglePlayPause()
                }) {
                    Image(systemName: videoService.isPlaying ? "pause.fill" : "play.fill")
                        .font(.system(size: 40, weight: .bold))
                        .foregroundColor(.white)
                        .frame(width: 80, height: 80)
                        .background(Color.white.opacity(0.2))
                        .clipShape(Circle())
                }
                .buttonStyle(TVButtonStyle())

                // Fast Forward Button
                Button(action: {
                    videoService.seekForward()
                }) {
                    Image(systemName: "goforward.10")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                }
                .buttonStyle(TVButtonStyle())
            }
        }
        .padding(.horizontal, 60)
        .padding(.bottom, 40)
    }
    
    // MARK: - Helper Methods
    
    private func setupPlayer() {
        print("🎬 Setting up player for URL: \(episode.url)")
        // Check for saved progress
        let savedProgress = videoService.getWatchProgress(for: movie, episode: episode) ?? 0
        print("🎬 Saved progress: \(savedProgress)")
        videoService.setupPlayer(with: episode.url, startTime: savedProgress)
    }

    private func setupFocus() {
        // Set focus to play/pause button
        let playButtonFocus = FocusableItem(
            id: "play_pause_button",
            type: .controlButton,
            index: 0,
            section: .videoPlayer
        )
        focusManager.setFocus(to: playButtonFocus, in: .videoPlayer)
    }

    private func saveWatchProgress() {
        videoService.saveWatchProgress(
            for: movie,
            episode: episode,
            currentTime: videoService.currentTime
        )
    }

    private func handleVideoEnd() {
        saveWatchProgress()

        // Check if there's a next episode
        if let episodes = movie.episodes,
           let currentIndex = episodes.firstIndex(where: { $0.id == episode.id }),
           currentIndex + 1 < episodes.count {
            // Auto-play next episode
            let nextEpisode = episodes[currentIndex + 1]
            navigationManager.currentEpisode = nextEpisode
            videoService.setupPlayer(with: nextEpisode.url)
        } else {
            // End of series, dismiss player
            navigationManager.dismissVideoPlayer()
        }
    }
    
    private func toggleControls() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showControls.toggle()
        }
        
        if showControls {
            startControlsTimer()
        } else {
            controlsTimer?.invalidate()
        }
    }
    
    private func startControlsTimer() {
        controlsTimer?.invalidate()
        controlsTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: false) { _ in
            withAnimation(.easeInOut(duration: 0.3)) {
                showControls = false
            }
        }
    }
}

// MARK: - Enhanced Video Progress View

struct EnhancedVideoProgressView: View {
    @StateObject private var videoService = VideoPlayerService.shared
    @State private var isDragging = false
    
    var body: some View {
        VStack(spacing: 8) {
            // Progress Bar
            HStack {
                Text(timeString(from: videoService.currentTime))
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 60, alignment: .leading)

                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // Background
                        Rectangle()
                            .fill(Color.white.opacity(0.3))
                            .frame(height: 4)

                        // Progress
                        Rectangle()
                            .fill(Color.white)
                            .frame(width: geometry.size.width * videoService.progress, height: 4)

                        // Buffered progress (if available)
                        Rectangle()
                            .fill(Color.white.opacity(0.5))
                            .frame(width: geometry.size.width * videoService.progress, height: 4)
                    }
                    .clipShape(RoundedRectangle(cornerRadius: 2))
                }
                .frame(height: 4)

                Text(timeString(from: videoService.duration))
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 60, alignment: .trailing)
            }

            // Additional info
            if videoService.isLive {
                Text("直播")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(Color.red.opacity(0.2))
                    .clipShape(RoundedRectangle(cornerRadius: 4))
            }
        }
    }
    

    
    private func timeString(from seconds: Double) -> String {
        guard !seconds.isNaN && !seconds.isInfinite else { return "00:00" }
        
        let totalSeconds = Int(seconds)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let secs = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, secs)
        } else {
            return String(format: "%02d:%02d", minutes, secs)
        }
    }
}



#Preview {
    VideoPlayerView(
        episode: Episode(episode: 1, title: "Test Episode", url: "https://example.com/video.m3u8"),
        movie: Movie.sample
    )
}
