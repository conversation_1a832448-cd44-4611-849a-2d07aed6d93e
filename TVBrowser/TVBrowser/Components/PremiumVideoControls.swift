//
//  PremiumVideoControls.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI
import AVKit

// MARK: - Premium Video Controls

struct PremiumVideoControls: View {
    @StateObject private var videoService = VideoPlayerService.shared
    @Environment(\.focusManager) private var focusManager
    
    @State private var showControls = true
    @State private var controlsTimer: Timer?
    @State private var currentFocusedControl = 0
    
    let movie: Movie
    let episode: Episode?
    
    private let controlButtons = ["rewind", "play_pause", "forward", "menu"]
    
    var body: some View {
        ZStack {
            // Background overlay
            if showControls {
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.black.opacity(0.8),
                                Color.black.opacity(0.4),
                                Color.black.opacity(0.8)
                            ],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .ignoresSafeArea()
                    .transition(.opacity)
            }
            
            VStack {
                // Top controls
                if showControls {
                    topControlsView
                        .transition(.move(edge: .top).combined(with: .opacity))
                }
                
                Spacer()
                
                // Center controls
                if showControls {
                    centerControlsView
                        .transition(.scale.combined(with: .opacity))
                }
                
                Spacer()
                
                // Bottom controls
                if showControls {
                    bottomControlsView
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                }
            }
        }
        .animation(DesignSystem.Animations.medium, value: showControls)
        .onAppear {
            startControlsTimer()
        }
        .onTapGesture {
            toggleControls()
        }
    }
    
    // MARK: - Top Controls
    
    private var topControlsView: some View {
        HStack {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                Text(movie.title)
                    .font(DesignSystem.Typography.headlineLarge)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
                
                if let episode = episode {
                    Text(episode.title)
                        .font(DesignSystem.Typography.titleMedium)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(1)
                }
                
                HStack(spacing: DesignSystem.Spacing.sm) {
                    if videoService.isLive {
                        LiveIndicator()
                    }
                    
                    QualityIndicator(quality: movie.remarks ?? "HD")
                    
                    if videoService.hasError {
                        ErrorIndicator()
                    }
                }
            }
            
            Spacer()
            
            // Close button
            Button(action: {
                // Close video player
            }) {
                Image(systemName: "xmark")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(Color.black.opacity(0.5))
                    )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, DesignSystem.Spacing.tvSafeArea)
        .padding(.top, DesignSystem.Spacing.xl)
    }
    
    // MARK: - Center Controls
    
    private var centerControlsView: some View {
        HStack(spacing: DesignSystem.Spacing.xxxl) {
            // Rewind button
            PremiumControlButton(
                icon: "gobackward.10",
                size: .large,
                isFocused: currentFocusedControl == 0
            ) {
                videoService.seekBackward()
                resetControlsTimer()
            }
            
            // Play/Pause button
            PremiumControlButton(
                icon: videoService.isPlaying ? "pause.fill" : "play.fill",
                size: .extraLarge,
                isFocused: currentFocusedControl == 1,
                isPrimary: true
            ) {
                videoService.togglePlayPause()
                resetControlsTimer()
            }
            
            // Forward button
            PremiumControlButton(
                icon: "goforward.10",
                size: .large,
                isFocused: currentFocusedControl == 2
            ) {
                videoService.seekForward()
                resetControlsTimer()
            }
        }
    }
    
    // MARK: - Bottom Controls
    
    private var bottomControlsView: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // Progress bar
            PremiumProgressBar(
                currentTime: videoService.currentTime,
                duration: videoService.duration,
                progress: videoService.progress,
                isLive: videoService.isLive
            )
            
            // Time and additional controls
            HStack {
                // Time display
                HStack(spacing: DesignSystem.Spacing.sm) {
                    Text(timeString(from: videoService.currentTime))
                        .font(DesignSystem.Typography.labelLarge)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .monospacedDigit()
                    
                    Text("/")
                        .font(DesignSystem.Typography.labelLarge)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Text(timeString(from: videoService.duration))
                        .font(DesignSystem.Typography.labelLarge)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .monospacedDigit()
                }
                
                Spacer()
                
                // Additional controls
                HStack(spacing: DesignSystem.Spacing.lg) {
                    // Subtitle button
                    if !videoService.availableSubtitles.isEmpty {
                        PremiumControlButton(
                            icon: "captions.bubble",
                            size: .small,
                            isFocused: currentFocusedControl == 3
                        ) {
                            // Show subtitle selection
                            resetControlsTimer()
                        }
                    }
                    
                    // Audio track button
                    if videoService.availableAudioTracks.count > 1 {
                        PremiumControlButton(
                            icon: "speaker.wave.2",
                            size: .small,
                            isFocused: currentFocusedControl == 4
                        ) {
                            // Show audio track selection
                            resetControlsTimer()
                        }
                    }
                    
                    // Settings button
                    PremiumControlButton(
                        icon: "gearshape",
                        size: .small,
                        isFocused: currentFocusedControl == 5
                    ) {
                        // Show settings
                        resetControlsTimer()
                    }
                }
            }
        }
        .padding(.horizontal, DesignSystem.Spacing.tvSafeArea)
        .padding(.bottom, DesignSystem.Spacing.xl)
    }
    
    // MARK: - Helper Methods
    
    private func toggleControls() {
        withAnimation(DesignSystem.Animations.medium) {
            showControls.toggle()
        }
        
        if showControls {
            startControlsTimer()
        } else {
            controlsTimer?.invalidate()
        }
    }
    
    private func startControlsTimer() {
        controlsTimer?.invalidate()
        controlsTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: false) { _ in
            withAnimation(DesignSystem.Animations.medium) {
                showControls = false
            }
        }
    }
    
    private func resetControlsTimer() {
        if showControls {
            startControlsTimer()
        }
    }
    
    private func timeString(from seconds: Double) -> String {
        let totalSeconds = Int(seconds)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let secs = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, secs)
        } else {
            return String(format: "%d:%02d", minutes, secs)
        }
    }
}

// MARK: - Premium Control Button

struct PremiumControlButton: View {
    let icon: String
    let size: ButtonSize
    let isFocused: Bool
    let isPrimary: Bool
    let action: () -> Void
    
    enum ButtonSize {
        case small, medium, large, extraLarge
        
        var iconSize: CGFloat {
            switch self {
            case .small: return 20
            case .medium: return 28
            case .large: return 36
            case .extraLarge: return 48
            }
        }
        
        var frameSize: CGFloat {
            switch self {
            case .small: return 44
            case .medium: return 60
            case .large: return 80
            case .extraLarge: return 100
            }
        }
    }
    
    init(
        icon: String,
        size: ButtonSize = .medium,
        isFocused: Bool = false,
        isPrimary: Bool = false,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.size = size
        self.isFocused = isFocused
        self.isPrimary = isPrimary
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            ZStack {
                // Background
                Circle()
                    .fill(
                        isPrimary ? 
                        DesignSystem.Colors.accent.opacity(isFocused ? 0.9 : 0.7) :
                        Color.black.opacity(isFocused ? 0.8 : 0.5)
                    )
                    .frame(width: size.frameSize, height: size.frameSize)
                
                // Focus ring
                if isFocused {
                    Circle()
                        .stroke(DesignSystem.Colors.focusRing, lineWidth: 3)
                        .frame(width: size.frameSize + 6, height: size.frameSize + 6)
                }
                
                // Icon
                Image(systemName: icon)
                    .font(.system(size: size.iconSize, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .offset(x: icon.contains("play") ? 2 : 0) // Optical alignment for play button
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isFocused ? 1.1 : 1.0)
        .animation(DesignSystem.Animations.focus, value: isFocused)
    }
}

// MARK: - Premium Progress Bar

struct PremiumProgressBar: View {
    let currentTime: Double
    let duration: Double
    let progress: Double
    let isLive: Bool
    
    @State private var isDragging = false
    @State private var dragProgress: Double = 0
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // Background track
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color.white.opacity(0.3))
                        .frame(height: 4)
                    
                    // Progress track
                    RoundedRectangle(cornerRadius: 2)
                        .fill(
                            LinearGradient(
                                colors: [DesignSystem.Colors.accent, DesignSystem.Colors.accent.opacity(0.8)],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(
                            width: geometry.size.width * (isDragging ? dragProgress : progress),
                            height: 4
                        )
                    
                    // Buffered progress (if available)
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color.white.opacity(0.5))
                        .frame(
                            width: geometry.size.width * progress * 1.1, // Simulated buffer
                            height: 4
                        )
                    
                    // Scrubber handle
                    if !isLive {
                        Circle()
                            .fill(DesignSystem.Colors.textPrimary)
                            .frame(width: 12, height: 12)
                            .offset(
                                x: geometry.size.width * (isDragging ? dragProgress : progress) - 6
                            )
                            .scaleEffect(isDragging ? 1.5 : 1.0)
                            .animation(DesignSystem.Animations.fast, value: isDragging)
                    }
                }
            }
            .frame(height: 12)
            .disabled(isLive)
        }
    }
}

// MARK: - Status Indicators

struct LiveIndicator: View {
    @State private var isPulsing = false
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.xs) {
            Circle()
                .fill(DesignSystem.Colors.error)
                .frame(width: 8, height: 8)
                .scaleEffect(isPulsing ? 1.2 : 1.0)
                .animation(AnimationSystem.pulseAnimation(), value: isPulsing)
                .onAppear { isPulsing = true }
            
            Text("直播")
                .font(DesignSystem.Typography.labelSmall)
                .foregroundColor(DesignSystem.Colors.error)
                .fontWeight(.semibold)
        }
        .padding(.horizontal, DesignSystem.Spacing.sm)
        .padding(.vertical, DesignSystem.Spacing.xs)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.xs)
                .fill(DesignSystem.Colors.error.opacity(0.2))
        )
    }
}

struct QualityIndicator: View {
    let quality: String
    
    var body: some View {
        Text(quality)
            .font(DesignSystem.Typography.labelSmall)
            .foregroundColor(DesignSystem.Colors.accent)
            .fontWeight(.semibold)
            .padding(.horizontal, DesignSystem.Spacing.sm)
            .padding(.vertical, DesignSystem.Spacing.xs)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.xs)
                    .fill(DesignSystem.Colors.accent.opacity(0.2))
            )
    }
}

struct ErrorIndicator: View {
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.xs) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.warning)
            
            Text("连接异常")
                .font(DesignSystem.Typography.labelSmall)
                .foregroundColor(DesignSystem.Colors.warning)
                .fontWeight(.medium)
        }
        .padding(.horizontal, DesignSystem.Spacing.sm)
        .padding(.vertical, DesignSystem.Spacing.xs)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.xs)
                .fill(DesignSystem.Colors.warning.opacity(0.2))
        )
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        PremiumVideoControls(
            movie: Movie.sample,
            episode: nil
        )
    }
}
