//
//  PremiumSearchComponents.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI

// MARK: - Premium Search Field

struct PremiumSearchField: View {
    @Binding var searchText: String
    @FocusState private var isFocused: Bool
    @State private var isAnimating = false
    
    let placeholder: String
    let onSubmit: () -> Void
    
    init(
        searchText: Binding<String>,
        placeholder: String = "输入影片名称...",
        onSubmit: @escaping () -> Void = {}
    ) {
        self._searchText = searchText
        self.placeholder = placeholder
        self.onSubmit = onSubmit
    }
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // Search icon
            Image(systemName: "magnifyingglass")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(isFocused ? DesignSystem.Colors.accent : DesignSystem.Colors.textSecondary)
                .scaleEffect(isFocused ? 1.1 : 1.0)
                .animation(DesignSystem.Animations.focus, value: isFocused)
            
            // Text field
            TextField(placeholder, text: $searchText)
                .font(DesignSystem.Typography.titleLarge)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .focused($isFocused)
                .textFieldStyle(PlainTextFieldStyle())
                .onSubmit(onSubmit)
            
            // Clear button
            if !searchText.isEmpty {
                Button(action: {
                    withAnimation(DesignSystem.Animations.fast) {
                        searchText = ""
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .buttonStyle(PlainButtonStyle())
                .transition(.scale.combined(with: .opacity))
            }
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.md)
                .fill(DesignSystem.Colors.surface)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.md)
                        .stroke(
                            isFocused ? DesignSystem.Colors.accent : DesignSystem.Colors.surface,
                            lineWidth: 2
                        )
                        .animation(DesignSystem.Animations.focus, value: isFocused)
                )
        )
        .scaleEffect(isFocused ? 1.02 : 1.0)
        .animation(DesignSystem.Animations.focus, value: isFocused)
    }
}

// MARK: - Premium Source Button

struct PremiumSourceButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    @Environment(\.focusManager) private var focusManager
    @State private var isFocused = false
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                // Selection indicator
                Circle()
                    .fill(isSelected ? DesignSystem.Colors.accent : Color.clear)
                    .frame(width: 8, height: 8)
                    .overlay(
                        Circle()
                            .stroke(
                                isSelected ? DesignSystem.Colors.accent : DesignSystem.Colors.textSecondary,
                                lineWidth: 2
                            )
                    )
                    .scaleEffect(isSelected ? 1.2 : 1.0)
                    .animation(DesignSystem.Animations.focus, value: isSelected)
                
                // Title
                Text(title)
                    .font(DesignSystem.Typography.labelLarge)
                    .foregroundColor(
                        isSelected ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary
                    )
                    .fontWeight(isSelected ? .semibold : .medium)
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.sm)
                    .fill(
                        isSelected ? DesignSystem.Colors.accent.opacity(0.2) :
                        isFocused ? DesignSystem.Colors.surface : Color.clear
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.sm)
                            .stroke(
                                isSelected ? DesignSystem.Colors.accent :
                                isFocused ? DesignSystem.Colors.focusRing : Color.clear,
                                lineWidth: isSelected || isFocused ? 1 : 0
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isFocused ? DesignSystem.Sizes.focusScaleSmall : 1.0)
        .animation(DesignSystem.Animations.focus, value: isFocused)
    }
}

// MARK: - Premium Search Result Card

struct PremiumSearchResultCard: View {
    let movie: Movie
    let index: Int
    let onTap: () -> Void
    
    @Environment(\.focusManager) private var focusManager
    @State private var isFocused = false
    @State private var imageLoadFailed = false
    @State private var animationDelay: Double = 0
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                // Poster
                posterView
                
                // Movie info
                movieInfoView
            }
            .frame(width: 160)
        }
        .buttonStyle(PlainButtonStyle())
        .focusAnimation(
            isFocused: isFocused,
            scale: DesignSystem.Sizes.focusScaleSmall,
            shadowRadius: 15
        )
        .onReceive(focusManager.$currentFocusedItem) { focusedItem in
            withAnimation(DesignSystem.Animations.focus) {
                isFocused = focusedItem?.type == .searchResult && 
                           focusedItem?.index == index &&
                           focusedItem?.section == .search
            }
        }
        .onAppear {
            animationDelay = Double(index % 10) * 0.05
            withAnimation(
                DesignSystem.Animations.slideIn.delay(animationDelay)
            ) {
                // Trigger appearance animation
            }
        }
    }
    
    private var posterView: some View {
        ZStack {
            AsyncImage(url: URL(string: movie.poster ?? "")) { phase in
                switch phase {
                case .empty:
                    Rectangle()
                        .fill(DesignSystem.Colors.surface)
                        .overlay(
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.textSecondary))
                        )
                        .shimmer()
                case .success(let image):
                    image
                        .resizable()
                        .aspectRatio(2/3, contentMode: .fill)
                case .failure(_):
                    Rectangle()
                        .fill(DesignSystem.Colors.surface)
                        .overlay(
                            Image(systemName: "photo")
                                .font(.system(size: 24, weight: .light))
                                .foregroundColor(DesignSystem.Colors.textTertiary)
                        )
                @unknown default:
                    Rectangle()
                        .fill(DesignSystem.Colors.surface)
                }
            }
            .frame(width: 160, height: 240)
            .clipShape(RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.sm))
            
            // Focus overlay
            if isFocused {
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.sm)
                    .stroke(DesignSystem.Colors.focusRing, lineWidth: 2)
                    .frame(width: 160, height: 240)
            }
        }
    }
    
    private var movieInfoView: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
            Text(movie.title)
                .font(DesignSystem.Typography.labelLarge)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            HStack(spacing: DesignSystem.Spacing.xs) {
                Text(movie.displayYear)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                if let remarks = movie.remarks, !remarks.isEmpty {
                    Text("•")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Text(remarks)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.accent)
                        .lineLimit(1)
                }
                
                Spacer()
            }
        }
        .frame(width: 160, alignment: .leading)
    }
}

// MARK: - Search State Views

struct PremiumSearchPrompt: View {
    @State private var isAnimating = false
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl) {
            // Animated icon
            ZStack {
                Circle()
                    .fill(DesignSystem.Colors.accent.opacity(0.1))
                    .frame(width: 120, height: 120)
                    .scaleEffect(isAnimating ? 1.1 : 1.0)
                    .animation(AnimationSystem.breathingAnimation(), value: isAnimating)
                
                Image(systemName: "tv")
                    .font(.system(size: 48, weight: .light))
                    .foregroundColor(DesignSystem.Colors.accent)
            }
            
            VStack(spacing: DesignSystem.Spacing.md) {
                Text("开始搜索")
                    .font(DesignSystem.Typography.displaySmall)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("输入影片名称来搜索您想观看的内容")
                    .font(DesignSystem.Typography.bodyLarge)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
        }
        .frame(maxHeight: 400)
        .onAppear {
            isAnimating = true
        }
    }
}

struct PremiumNoResults: View {
    let query: String
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            VStack(spacing: DesignSystem.Spacing.md) {
                Text("未找到相关影片")
                    .font(DesignSystem.Typography.displaySmall)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("尝试使用不同的关键词或更换数据源")
                    .font(DesignSystem.Typography.bodyLarge)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                
                if !query.isEmpty {
                    Text("搜索词: \"\(query)\"")
                        .font(DesignSystem.Typography.labelMedium)
                        .foregroundColor(DesignSystem.Colors.textTertiary)
                        .padding(.top, DesignSystem.Spacing.sm)
                }
            }
        }
        .frame(maxHeight: 400)
    }
}

struct PremiumSearchLoading: View {
    @State private var rotationAngle: Double = 0
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xl) {
            ZStack {
                Circle()
                    .stroke(DesignSystem.Colors.surface, lineWidth: 4)
                    .frame(width: 60, height: 60)
                
                Circle()
                    .trim(from: 0, to: 0.3)
                    .stroke(DesignSystem.Colors.accent, lineWidth: 4)
                    .frame(width: 60, height: 60)
                    .rotationEffect(.degrees(rotationAngle))
                    .animation(AnimationSystem.rotationAnimation(), value: rotationAngle)
            }
            
            VStack(spacing: DesignSystem.Spacing.sm) {
                Text("搜索中...")
                    .font(DesignSystem.Typography.headlineMedium)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                PulsingDots()
            }
        }
        .frame(maxHeight: 300)
        .onAppear {
            rotationAngle = 360
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 30) {
        PremiumSearchField(
            searchText: .constant("复仇者联盟"),
            onSubmit: {}
        )
        
        HStack(spacing: 15) {
            PremiumSourceButton(
                title: "黑木耳",
                isSelected: true,
                action: {}
            )
            
            PremiumSourceButton(
                title: "暴风资源",
                isSelected: false,
                action: {}
            )
        }
        
        PremiumSearchResultCard(
            movie: Movie.sample,
            index: 0,
            onTap: {}
        )
    }
    .padding()
    .designSystemBackground()
}
