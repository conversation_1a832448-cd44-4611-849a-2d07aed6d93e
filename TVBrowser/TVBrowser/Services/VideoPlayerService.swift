//
//  VideoPlayerService.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import Foundation
import AVFoundation
import Combine

// MARK: - Video Player Errors

enum VideoPlayerError: LocalizedError {
    case invalidURL(String)
    case networkError(String)
    case playbackFailed(String)
    case unsupportedFormat(String)
    case loadingTimeout
    case playerNotReady
    
    var errorDescription: String? {
        switch self {
        case .invalidURL(let url):
            return "无效的视频地址: \(url)"
        case .networkError(let message):
            return "网络错误: \(message)"
        case .playbackFailed(let message):
            return "播放失败: \(message)"
        case .unsupportedFormat(let format):
            return "不支持的视频格式: \(format)"
        case .loadingTimeout:
            return "视频加载超时，请检查网络连接"
        case .playerNotReady:
            return "播放器未准备就绪"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .invalidURL:
            return "请检查视频源是否正确"
        case .networkError:
            return "请检查网络连接后重试"
        case .playbackFailed:
            return "请尝试切换视频源或重新加载"
        case .unsupportedFormat:
            return "请联系开发者支持此格式"
        case .loadingTimeout:
            return "请检查网络连接或稍后重试"
        case .playerNotReady:
            return "请稍等片刻后重试"
        }
    }
}

// MARK: - Video Player Service

class VideoPlayerService: NSObject, ObservableObject {
    static let shared = VideoPlayerService()
    
    // Player state
    @Published var player: AVPlayer?
    @Published var isPlaying = false
    @Published var isLoading = false
    @Published var hasError = false
    @Published var errorMessage: String?
    @Published var currentError: VideoPlayerError?
    
    // Playback progress
    @Published var currentTime: Double = 0
    @Published var duration: Double = 0
    @Published var progress: Double = 0
    @Published var isLive = false
    
    // Video quality
    @Published var availableQualities: [VideoQuality] = []
    @Published var currentQuality: VideoQuality?
    @Published var isAdaptiveStreaming = true
    
    // Playback controls
    @Published var playbackRate: Float = 1.0
    @Published var volume: Float = 1.0
    @Published var isMuted = false
    
    // Subtitle support
    @Published var availableSubtitles: [SubtitleTrack] = []
    @Published var currentSubtitle: SubtitleTrack?
    @Published var subtitlesEnabled = false
    
    // Audio tracks
    @Published var availableAudioTracks: [AudioTrack] = []
    @Published var currentAudioTrack: AudioTrack?
    
    // Playback history
    @Published var watchHistory: [WatchHistoryItem] = []
    
    // Enhanced error handling
    @Published var retryCount = 0
    @Published var maxRetries = 3
    @Published var canRetry = true
    
    private var timeObserver: Any?
    private var cancellables = Set<AnyCancellable>()
    private var currentItem: AVPlayerItem?
    private var loadingTimer: Timer?
    private let loadingTimeout: TimeInterval = 30.0
    
    // Observer tracking to prevent crashes
    private var isObservingPlayerItem = false
    private var isObservingPlayer = false
    private var observedPlayerItem: AVPlayerItem?
    private var observedPlayer: AVPlayer?
    
    override init() {
        super.init()
        setupNotificationObservers()
        loadWatchHistory()
    }
    
    // MARK: - Player Setup with Enhanced Error Handling
    
    func setupPlayer(with urlString: String, startTime: Double = 0) {
        print("🎬 VideoPlayerService: Setting up player with URL: \(urlString)")
        
        // Reset error state
        clearError()
        
        guard !urlString.isEmpty else {
            handleError(.invalidURL("URL为空"))
            return
        }

        guard let url = URL(string: urlString) else {
            print("🎬 VideoPlayerService: Invalid URL: \(urlString)")
            handleError(.invalidURL(urlString))
            return
        }

        print("🎬 VideoPlayerService: URL is valid, creating player")
        isLoading = true
        
        // Start loading timeout timer
        startLoadingTimer()

        // Clean up existing observers before creating new ones
        removeAllObservers()

        // Create player item with better error handling
        let playerItem = AVPlayerItem(url: url)
        currentItem = playerItem

        // Setup player
        if player == nil {
            print("🎬 VideoPlayerService: Creating new AVPlayer")
            player = AVPlayer(playerItem: playerItem)
        } else {
            print("🎬 VideoPlayerService: Replacing current item")
            player?.replaceCurrentItem(with: playerItem)
        }

        // Setup observers
        setupPlayerObservers()
        setupTimeObserver()

        // Seek to start time if provided
        if startTime > 0 {
            print("🎬 VideoPlayerService: Seeking to start time: \(startTime)")
            seek(to: startTime)
        }

        // Auto-play with delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            print("🎬 VideoPlayerService: Auto-playing video")
            self.play()
        }
    }
    
    private func startLoadingTimer() {
        loadingTimer?.invalidate()
        loadingTimer = Timer.scheduledTimer(withTimeInterval: loadingTimeout, repeats: false) { [weak self] _ in
            if self?.isLoading == true {
                self?.handleError(.loadingTimeout)
            }
        }
    }
    
    private func stopLoadingTimer() {
        loadingTimer?.invalidate()
        loadingTimer = nil
    }
    
    private func setupPlayerObservers() {
        guard let playerItem = currentItem, let player = player else { return }
        
        print("🎬 VideoPlayerService: Setting up observers")
        
        // Remove existing observers first
        removeAllObservers()
        
        // Setup player item observers
        do {
            playerItem.addObserver(self, forKeyPath: "status", options: [.new], context: nil)
            playerItem.addObserver(self, forKeyPath: "duration", options: [.new], context: nil)
            playerItem.addObserver(self, forKeyPath: "loadedTimeRanges", options: [.new], context: nil)
            
            isObservingPlayerItem = true
            observedPlayerItem = playerItem
            print("🎬 VideoPlayerService: Player item observers added")
        }
        
        // Setup player observers
        do {
            player.addObserver(self, forKeyPath: "timeControlStatus", options: [.new], context: nil)
            player.addObserver(self, forKeyPath: "rate", options: [.new], context: nil)
            
            isObservingPlayer = true
            observedPlayer = player
            print("🎬 VideoPlayerService: Player observers added")
        }
        
        // Setup notification observers
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerDidFinishPlaying),
            name: .AVPlayerItemDidPlayToEndTime,
            object: playerItem
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerDidStall),
            name: .AVPlayerItemPlaybackStalled,
            object: playerItem
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerFailedToPlayToEnd),
            name: .AVPlayerItemFailedToPlayToEndTime,
            object: playerItem
        )
    }
    
    private func removeAllObservers() {
        print("🎬 VideoPlayerService: Removing all observers")
        
        // Remove player item observers safely
        if isObservingPlayerItem, let playerItem = observedPlayerItem {
            do {
                playerItem.removeObserver(self, forKeyPath: "status")
                playerItem.removeObserver(self, forKeyPath: "duration")
                playerItem.removeObserver(self, forKeyPath: "loadedTimeRanges")
                print("🎬 VideoPlayerService: Player item observers removed")
            } catch {
                print("🎬 VideoPlayerService: Error removing player item observers: \(error)")
            }
        }
        
        // Remove player observers safely
        if isObservingPlayer, let player = observedPlayer {
            do {
                player.removeObserver(self, forKeyPath: "timeControlStatus")
                player.removeObserver(self, forKeyPath: "rate")
                print("🎬 VideoPlayerService: Player observers removed")
            } catch {
                print("🎬 VideoPlayerService: Error removing player observers: \(error)")
            }
        }
        
        // Reset observer tracking
        isObservingPlayerItem = false
        isObservingPlayer = false
        observedPlayerItem = nil
        observedPlayer = nil
        
        // Remove notification observers
        NotificationCenter.default.removeObserver(self)
        print("🎬 VideoPlayerService: All observers removed")
    }
    
    private func setupTimeObserver() {
        guard let player = player else { return }
        
        // Remove existing time observer
        if let timeObserver = timeObserver {
            player.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }
        
        let interval = CMTime(seconds: 1.0, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        timeObserver = player.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            self?.updatePlaybackProgress(time)
        }
    }
    
    // MARK: - Enhanced Playback Controls
    
    func play() {
        guard let player = player else {
            handleError(.playerNotReady)
            return
        }
        
        player.play()
        isPlaying = true
    }
    
    func pause() {
        player?.pause()
        isPlaying = false
    }
    
    func togglePlayPause() {
        if isPlaying {
            pause()
        } else {
            play()
        }
    }
    
    func seek(to time: Double) {
        guard let player = player else { return }
        let cmTime = CMTime(seconds: time, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        player.seek(to: cmTime) { [weak self] completed in
            if completed {
                self?.updateCurrentTime(time)
            }
        }
    }
    
    func seekForward(_ seconds: Double = 10) {
        let newTime = currentTime + seconds
        seek(to: min(newTime, duration))
    }
    
    func seekBackward(_ seconds: Double = 10) {
        let newTime = currentTime - seconds
        seek(to: max(newTime, 0))
    }
    
    func setPlaybackRate(_ rate: Float) {
        player?.rate = rate
        playbackRate = rate
    }
    
    func setVolume(_ volume: Float) {
        player?.volume = volume
        self.volume = volume
    }
    
    func toggleMute() {
        isMuted.toggle()
        player?.isMuted = isMuted
    }
    
    // MARK: - Error Handling and Retry Logic
    
    private func clearError() {
        hasError = false
        errorMessage = nil
        currentError = nil
        canRetry = true
    }
    
    private func handleError(_ error: VideoPlayerError) {
        print("🎬 VideoPlayerService Error: \(error.localizedDescription)")
        
        DispatchQueue.main.async {
            self.hasError = true
            self.errorMessage = error.localizedDescription
            self.currentError = error
            self.isLoading = false
            self.stopLoadingTimer()
            
            // Determine if retry is possible
            self.canRetry = self.retryCount < self.maxRetries && self.shouldAllowRetry(for: error)
        }
    }
    
    private func shouldAllowRetry(for error: VideoPlayerError) -> Bool {
        switch error {
        case .networkError, .loadingTimeout, .playbackFailed:
            return true
        case .invalidURL, .unsupportedFormat, .playerNotReady:
            return false
        }
    }
    
    func retryPlayback() {
        guard canRetry, retryCount < maxRetries else {
            print("🎬 VideoPlayerService: Max retries reached or retry not allowed")
            return
        }
        
        retryCount += 1
        print("🎬 VideoPlayerService: Retrying playback (attempt \(retryCount)/\(maxRetries))")
        
        // Get the current URL to retry
        if let currentURL = currentItem?.asset as? AVURLAsset {
            setupPlayer(with: currentURL.url.absoluteString)
        }
    }
    
    func resetRetryCount() {
        retryCount = 0
    }
    
    // MARK: - Quality Management
    
    func setVideoQuality(_ quality: VideoQuality) {
        currentQuality = quality
        // Implement quality switching logic
        if let player = player, let playerItem = player.currentItem {
            // Switch to specific quality if available
            // This would require HLS manifest parsing for adaptive streaming
        }
    }
    
    func enableAdaptiveStreaming(_ enabled: Bool) {
        isAdaptiveStreaming = enabled
        // Configure player for adaptive streaming
    }
    
    // MARK: - Subtitle Management
    
    func enableSubtitles(_ enabled: Bool) {
        subtitlesEnabled = enabled
        
        if enabled, let subtitle = currentSubtitle {
            selectSubtitleTrack(subtitle)
        } else {
            // Disable subtitles
            if let playerItem = player?.currentItem,
               let group = playerItem.asset.mediaSelectionGroup(forMediaCharacteristic: .legible) {
                playerItem.select(nil, in: group)
            }
        }
    }
    
    func selectSubtitleTrack(_ track: SubtitleTrack) {
        currentSubtitle = track
        
        guard let playerItem = player?.currentItem,
              let group = playerItem.asset.mediaSelectionGroup(forMediaCharacteristic: .legible) else {
            return
        }
        
        if let option = group.options.first(where: { $0.displayName == track.name }) {
            playerItem.select(option, in: group)
        }
    }
    
    // MARK: - Audio Track Management
    
    func selectAudioTrack(_ track: AudioTrack) {
        currentAudioTrack = track
        
        guard let playerItem = player?.currentItem,
              let group = playerItem.asset.mediaSelectionGroup(forMediaCharacteristic: .audible) else {
            return
        }
        
        if let option = group.options.first(where: { $0.displayName == track.name }) {
            playerItem.select(option, in: group)
        }
    }
    
    // MARK: - Watch History
    
    func saveWatchProgress(for movie: Movie, episode: Episode?, currentTime: Double) {
        let historyItem = WatchHistoryItem(
            movieId: String(movie.id),
            movieTitle: movie.title,
            episodeId: episode?.id.uuidString,
            episodeTitle: episode?.title,
            currentTime: currentTime,
            duration: duration,
            lastWatched: Date()
        )
        
        // Remove existing entry for same content
        watchHistory.removeAll { item in
            item.movieId == String(movie.id) && item.episodeId == episode?.id.uuidString
        }
        
        // Add new entry
        watchHistory.insert(historyItem, at: 0)
        
        // Limit history size
        if watchHistory.count > 100 {
            watchHistory = Array(watchHistory.prefix(100))
        }
        
        saveWatchHistory()
    }
    
    func getWatchProgress(for movie: Movie, episode: Episode?) -> Double? {
        let historyItem = watchHistory.first { item in
            item.movieId == String(movie.id) && item.episodeId == episode?.id.uuidString
        }
        return historyItem?.currentTime
    }
    
    private func loadWatchHistory() {
        if let data = UserDefaults.standard.data(forKey: "watchHistory"),
           let history = try? JSONDecoder().decode([WatchHistoryItem].self, from: data) {
            watchHistory = history
        }
    }
    
    private func saveWatchHistory() {
        if let data = try? JSONEncoder().encode(watchHistory) {
            UserDefaults.standard.set(data, forKey: "watchHistory")
        }
    }
    
    // MARK: - Helper Methods
    
    private func updatePlaybackProgress(_ time: CMTime) {
        let timeInSeconds = time.seconds
        guard !timeInSeconds.isNaN && !timeInSeconds.isInfinite else { return }
        
        currentTime = timeInSeconds
        
        if duration > 0 {
            progress = currentTime / duration
        }
    }
    
    private func updateCurrentTime(_ time: Double) {
        currentTime = time
        if duration > 0 {
            progress = currentTime / duration
        }
    }
    
    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleTogglePlayPause),
            name: .videoPlayerTogglePlayPause,
            object: nil
        )
    }
    
    @objc private func handleTogglePlayPause() {
        togglePlayPause()
    }
    
    @objc private func playerDidFinishPlaying() {
        isPlaying = false
        resetRetryCount() // Reset retry count on successful completion
        // Auto-advance to next episode if available
        NotificationCenter.default.post(name: .videoPlayerDidFinish, object: nil)
    }
    
    @objc private func playerDidStall() {
        isLoading = true
        print("🎬 VideoPlayerService: Player stalled, buffering...")
    }
    
    @objc private func playerFailedToPlayToEnd() {
        handleError(.playbackFailed("播放中断"))
    }
    
    // MARK: - KVO Observer with Enhanced Error Handling
    
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        DispatchQueue.main.async {
            switch keyPath {
            case "status":
                if let playerItem = object as? AVPlayerItem {
                    switch playerItem.status {
                    case .readyToPlay:
                        print("🎬 VideoPlayerService: Player ready to play")
                        self.isLoading = false
                        self.stopLoadingTimer()
                        self.duration = playerItem.duration.seconds
                        self.loadAvailableTracks()
                        self.resetRetryCount() // Reset on successful load
                    case .failed:
                        let errorDescription = playerItem.error?.localizedDescription ?? "Unknown playback error"
                        print("🎬 VideoPlayerService: Player failed: \(errorDescription)")
                        self.handleError(.playbackFailed(errorDescription))
                    case .unknown:
                        print("🎬 VideoPlayerService: Player status unknown")
                    @unknown default:
                        print("🎬 VideoPlayerService: Unknown player status")
                    }
                }
            case "timeControlStatus":
                if let player = object as? AVPlayer {
                    switch player.timeControlStatus {
                    case .playing:
                        self.isPlaying = true
                        self.isLoading = false
                    case .paused:
                        self.isPlaying = false
                        self.isLoading = false
                    case .waitingToPlayAtSpecifiedRate:
                        self.isLoading = true
                    @unknown default:
                        break
                    }
                }
            case "duration":
                if let playerItem = object as? AVPlayerItem {
                    let newDuration = playerItem.duration.seconds
                    if !newDuration.isNaN && !newDuration.isInfinite {
                        self.duration = newDuration
                    }
                }
            default:
                break
            }
        }
    }
    
    private func loadAvailableTracks() {
        guard let playerItem = currentItem else { return }
        
        // Load subtitle tracks
        if let legibleGroup = playerItem.asset.mediaSelectionGroup(forMediaCharacteristic: .legible) {
            availableSubtitles = legibleGroup.options.map { option in
                SubtitleTrack(
                    id: option.displayName,
                    name: option.displayName,
                    language: option.locale?.language.languageCode?.identifier ?? "unknown"
                )
            }
        }

        // Load audio tracks
        if let audibleGroup = playerItem.asset.mediaSelectionGroup(forMediaCharacteristic: .audible) {
            availableAudioTracks = audibleGroup.options.map { option in
                AudioTrack(
                    id: option.displayName,
                    name: option.displayName,
                    language: option.locale?.language.languageCode?.identifier ?? "unknown"
                )
            }
        }
    }
    
    // MARK: - Enhanced Cleanup
    
    func cleanup() {
        print("🎬 VideoPlayerService: Cleaning up")
        
        player?.pause()
        
        // Remove time observer safely
        if let timeObserver = timeObserver, let player = player {
            player.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }
        
        // Remove all KVO and notification observers
        removeAllObservers()
        stopLoadingTimer()
        
        player = nil
        currentItem = nil
        
        // Reset state
        isPlaying = false
        isLoading = false
        currentTime = 0
        duration = 0
        progress = 0
        
        // Don't reset error state on cleanup - let user see the error
    }
    
    deinit {
        print("🎬 VideoPlayerService: Deinitializing")
        cleanup()
    }
}

// MARK: - Supporting Models (unchanged)

struct VideoQuality: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let resolution: String
    let bitrate: Int
}

struct SubtitleTrack: Identifiable, Hashable {
    let id: String
    let name: String
    let language: String
}

struct AudioTrack: Identifiable, Hashable {
    let id: String
    let name: String
    let language: String
}

struct WatchHistoryItem: Codable, Identifiable {
    var id = UUID()
    let movieId: String
    let movieTitle: String
    let episodeId: String?
    let episodeTitle: String?
    let currentTime: Double
    let duration: Double
    let lastWatched: Date
    
    var progressPercentage: Double {
        guard duration > 0 else { return 0 }
        return (currentTime / duration) * 100
    }
    
    var isCompleted: Bool {
        progressPercentage > 90
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let videoPlayerDidFinish = Notification.Name("videoPlayerDidFinish")
    static let videoPlayerTogglePlayPause = Notification.Name("videoPlayerTogglePlayPause")
}
