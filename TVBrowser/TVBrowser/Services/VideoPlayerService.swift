//
//  VideoPlayerService.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import Foundation
import AVFoundation
import Combine

// MARK: - Video Player Service

class VideoPlayerService: NSObject, ObservableObject {
    static let shared = VideoPlayerService()
    
    // Player state
    @Published var player: AVPlayer?
    @Published var isPlaying = false
    @Published var isLoading = false
    @Published var hasError = false
    @Published var errorMessage: String?
    
    // Playback progress
    @Published var currentTime: Double = 0
    @Published var duration: Double = 0
    @Published var progress: Double = 0
    @Published var isLive = false
    
    // Video quality
    @Published var availableQualities: [VideoQuality] = []
    @Published var currentQuality: VideoQuality?
    @Published var isAdaptiveStreaming = true
    
    // Playback controls
    @Published var playbackRate: Float = 1.0
    @Published var volume: Float = 1.0
    @Published var isMuted = false
    
    // Subtitle support
    @Published var availableSubtitles: [SubtitleTrack] = []
    @Published var currentSubtitle: SubtitleTrack?
    @Published var subtitlesEnabled = false
    
    // Audio tracks
    @Published var availableAudioTracks: [AudioTrack] = []
    @Published var currentAudioTrack: AudioTrack?
    
    // Playback history
    @Published var watchHistory: [WatchHistoryItem] = []
    
    private var timeObserver: Any?
    private var cancellables = Set<AnyCancellable>()
    private var currentItem: AVPlayerItem?
    
    override init() {
        super.init()
        setupNotificationObservers()
        loadWatchHistory()
    }
    
    // MARK: - Player Setup
    
    func setupPlayer(with urlString: String, startTime: Double = 0) {
        print("🎬 VideoPlayerService: Setting up player with URL: \(urlString)")

        guard let url = URL(string: urlString) else {
            print("🎬 VideoPlayerService: Invalid URL: \(urlString)")
            handleError("Invalid video URL")
            return
        }

        print("🎬 VideoPlayerService: URL is valid, creating player")
        isLoading = true
        hasError = false
        errorMessage = nil

        // Create player item
        let playerItem = AVPlayerItem(url: url)
        currentItem = playerItem

        // Setup player
        if player == nil {
            print("🎬 VideoPlayerService: Creating new AVPlayer")
            player = AVPlayer(playerItem: playerItem)
        } else {
            print("🎬 VideoPlayerService: Replacing current item")
            player?.replaceCurrentItem(with: playerItem)
        }

        // Setup observers
        setupPlayerObservers()
        setupTimeObserver()

        // Seek to start time if provided
        if startTime > 0 {
            print("🎬 VideoPlayerService: Seeking to start time: \(startTime)")
            seek(to: startTime)
        }

        // Auto-play
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            print("🎬 VideoPlayerService: Auto-playing video")
            self.play()
        }
    }
    
    private func setupPlayerObservers() {
        guard let playerItem = currentItem else { return }
        
        // Observe player item status
        playerItem.addObserver(self, forKeyPath: "status", options: [.new], context: nil)
        playerItem.addObserver(self, forKeyPath: "duration", options: [.new], context: nil)
        playerItem.addObserver(self, forKeyPath: "loadedTimeRanges", options: [.new], context: nil)
        
        // Observe player status
        player?.addObserver(self, forKeyPath: "timeControlStatus", options: [.new], context: nil)
        player?.addObserver(self, forKeyPath: "rate", options: [.new], context: nil)
        
        // Setup notification observers
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerDidFinishPlaying),
            name: .AVPlayerItemDidPlayToEndTime,
            object: playerItem
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerDidStall),
            name: .AVPlayerItemPlaybackStalled,
            object: playerItem
        )
    }
    
    private func setupTimeObserver() {
        guard let player = player else { return }
        
        let interval = CMTime(seconds: 1.0, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        timeObserver = player.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            self?.updatePlaybackProgress(time)
        }
    }
    
    // MARK: - Playback Controls
    
    func play() {
        player?.play()
        isPlaying = true
    }
    
    func pause() {
        player?.pause()
        isPlaying = false
    }
    
    func togglePlayPause() {
        if isPlaying {
            pause()
        } else {
            play()
        }
    }
    
    func seek(to time: Double) {
        guard let player = player else { return }
        let cmTime = CMTime(seconds: time, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        player.seek(to: cmTime) { [weak self] completed in
            if completed {
                self?.updateCurrentTime(time)
            }
        }
    }
    
    func seekForward(_ seconds: Double = 10) {
        let newTime = currentTime + seconds
        seek(to: min(newTime, duration))
    }
    
    func seekBackward(_ seconds: Double = 10) {
        let newTime = currentTime - seconds
        seek(to: max(newTime, 0))
    }
    
    func setPlaybackRate(_ rate: Float) {
        player?.rate = rate
        playbackRate = rate
    }
    
    func setVolume(_ volume: Float) {
        player?.volume = volume
        self.volume = volume
    }
    
    func toggleMute() {
        isMuted.toggle()
        player?.isMuted = isMuted
    }
    
    // MARK: - Quality Management
    
    func setVideoQuality(_ quality: VideoQuality) {
        currentQuality = quality
        // Implement quality switching logic
        if let player = player, let playerItem = player.currentItem {
            // Switch to specific quality if available
            // This would require HLS manifest parsing for adaptive streaming
        }
    }
    
    func enableAdaptiveStreaming(_ enabled: Bool) {
        isAdaptiveStreaming = enabled
        // Configure player for adaptive streaming
    }
    
    // MARK: - Subtitle Management
    
    func enableSubtitles(_ enabled: Bool) {
        subtitlesEnabled = enabled
        
        if enabled, let subtitle = currentSubtitle {
            selectSubtitleTrack(subtitle)
        } else {
            // Disable subtitles
            if let playerItem = player?.currentItem,
               let group = playerItem.asset.mediaSelectionGroup(forMediaCharacteristic: .legible) {
                playerItem.select(nil, in: group)
            }
        }
    }
    
    func selectSubtitleTrack(_ track: SubtitleTrack) {
        currentSubtitle = track
        
        guard let playerItem = player?.currentItem,
              let group = playerItem.asset.mediaSelectionGroup(forMediaCharacteristic: .legible) else {
            return
        }
        
        if let option = group.options.first(where: { $0.displayName == track.name }) {
            playerItem.select(option, in: group)
        }
    }
    
    // MARK: - Audio Track Management
    
    func selectAudioTrack(_ track: AudioTrack) {
        currentAudioTrack = track
        
        guard let playerItem = player?.currentItem,
              let group = playerItem.asset.mediaSelectionGroup(forMediaCharacteristic: .audible) else {
            return
        }
        
        if let option = group.options.first(where: { $0.displayName == track.name }) {
            playerItem.select(option, in: group)
        }
    }
    
    // MARK: - Watch History
    
    func saveWatchProgress(for movie: Movie, episode: Episode?, currentTime: Double) {
        let historyItem = WatchHistoryItem(
            movieId: String(movie.id),
            movieTitle: movie.title,
            episodeId: episode?.id.uuidString,
            episodeTitle: episode?.title,
            currentTime: currentTime,
            duration: duration,
            lastWatched: Date()
        )
        
        // Remove existing entry for same content
        watchHistory.removeAll { item in
            item.movieId == String(movie.id) && item.episodeId == episode?.id.uuidString
        }
        
        // Add new entry
        watchHistory.insert(historyItem, at: 0)
        
        // Limit history size
        if watchHistory.count > 100 {
            watchHistory = Array(watchHistory.prefix(100))
        }
        
        saveWatchHistory()
    }
    
    func getWatchProgress(for movie: Movie, episode: Episode?) -> Double? {
        let historyItem = watchHistory.first { item in
            item.movieId == String(movie.id) && item.episodeId == episode?.id.uuidString
        }
        return historyItem?.currentTime
    }
    
    private func loadWatchHistory() {
        if let data = UserDefaults.standard.data(forKey: "watchHistory"),
           let history = try? JSONDecoder().decode([WatchHistoryItem].self, from: data) {
            watchHistory = history
        }
    }
    
    private func saveWatchHistory() {
        if let data = try? JSONEncoder().encode(watchHistory) {
            UserDefaults.standard.set(data, forKey: "watchHistory")
        }
    }
    
    // MARK: - Helper Methods
    
    private func updatePlaybackProgress(_ time: CMTime) {
        let timeInSeconds = time.seconds
        guard !timeInSeconds.isNaN && !timeInSeconds.isInfinite else { return }
        
        currentTime = timeInSeconds
        
        if duration > 0 {
            progress = currentTime / duration
        }
    }
    
    private func updateCurrentTime(_ time: Double) {
        currentTime = time
        if duration > 0 {
            progress = currentTime / duration
        }
    }
    
    private func handleError(_ message: String) {
        hasError = true
        errorMessage = message
        isLoading = false
        print("VideoPlayerService Error: \(message)")
    }
    
    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleTogglePlayPause),
            name: .videoPlayerTogglePlayPause,
            object: nil
        )
    }
    
    @objc private func handleTogglePlayPause() {
        togglePlayPause()
    }
    
    @objc private func playerDidFinishPlaying() {
        isPlaying = false
        // Auto-advance to next episode if available
        NotificationCenter.default.post(name: .videoPlayerDidFinish, object: nil)
    }
    
    @objc private func playerDidStall() {
        isLoading = true
    }
    
    // MARK: - KVO Observer
    
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        DispatchQueue.main.async {
            switch keyPath {
            case "status":
                if let playerItem = object as? AVPlayerItem {
                    switch playerItem.status {
                    case .readyToPlay:
                        self.isLoading = false
                        self.duration = playerItem.duration.seconds
                        self.loadAvailableTracks()
                    case .failed:
                        self.handleError(playerItem.error?.localizedDescription ?? "Playback failed")
                    default:
                        break
                    }
                }
            case "timeControlStatus":
                if let player = object as? AVPlayer {
                    switch player.timeControlStatus {
                    case .playing:
                        self.isPlaying = true
                        self.isLoading = false
                    case .paused:
                        self.isPlaying = false
                        self.isLoading = false
                    case .waitingToPlayAtSpecifiedRate:
                        self.isLoading = true
                    @unknown default:
                        break
                    }
                }
            case "duration":
                if let playerItem = object as? AVPlayerItem {
                    self.duration = playerItem.duration.seconds
                }
            default:
                break
            }
        }
    }
    
    private func loadAvailableTracks() {
        guard let playerItem = currentItem else { return }
        
        // Load subtitle tracks
        if let legibleGroup = playerItem.asset.mediaSelectionGroup(forMediaCharacteristic: .legible) {
            availableSubtitles = legibleGroup.options.map { option in
                SubtitleTrack(
                    id: option.displayName,
                    name: option.displayName,
                    language: option.locale?.language.languageCode?.identifier ?? "unknown"
                )
            }
        }

        // Load audio tracks
        if let audibleGroup = playerItem.asset.mediaSelectionGroup(forMediaCharacteristic: .audible) {
            availableAudioTracks = audibleGroup.options.map { option in
                AudioTrack(
                    id: option.displayName,
                    name: option.displayName,
                    language: option.locale?.language.languageCode?.identifier ?? "unknown"
                )
            }
        }
    }
    
    // MARK: - Cleanup
    
    func cleanup() {
        player?.pause()
        
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
        }
        
        currentItem?.removeObserver(self, forKeyPath: "status")
        currentItem?.removeObserver(self, forKeyPath: "duration")
        currentItem?.removeObserver(self, forKeyPath: "loadedTimeRanges")
        
        player?.removeObserver(self, forKeyPath: "timeControlStatus")
        player?.removeObserver(self, forKeyPath: "rate")
        
        NotificationCenter.default.removeObserver(self)
        
        player = nil
        currentItem = nil
    }
    
    deinit {
        cleanup()
    }
}

// MARK: - Supporting Models

struct VideoQuality: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let resolution: String
    let bitrate: Int
}

struct SubtitleTrack: Identifiable, Hashable {
    let id: String
    let name: String
    let language: String
}

struct AudioTrack: Identifiable, Hashable {
    let id: String
    let name: String
    let language: String
}

struct WatchHistoryItem: Codable, Identifiable {
    var id = UUID()
    let movieId: String
    let movieTitle: String
    let episodeId: String?
    let episodeTitle: String?
    let currentTime: Double
    let duration: Double
    let lastWatched: Date
    
    var progressPercentage: Double {
        guard duration > 0 else { return 0 }
        return (currentTime / duration) * 100
    }
    
    var isCompleted: Bool {
        progressPercentage > 90
    }
}

// MARK: - Additional Notification Names

extension Notification.Name {
    static let videoPlayerDidFinish = Notification.Name("videoPlayerDidFinish")
}
