//
//  APIService.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import Foundation
import Combine

class APIService: ObservableObject {
    static let shared = APIService()
    
    private let baseURL = "http://localhost:8080/api"
    private let session = URLSession.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {}
    
    // MARK: - API Methods
    
    func fetchRecommendations() -> AnyPublisher<[Movie], APIError> {
        guard let url = URL(string: "\(baseURL)/recommendations") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: RecommendationsResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    func searchMovies(query: String, source: String = "heimuer") -> AnyPublisher<[Movie], APIError> {
        guard let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
              let url = URL(string: "\(baseURL)/search?q=\(encodedQuery)&source=\(source)") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }

        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: SearchResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }

    func searchMoviesAggregated(query: String, includeAdult: Bool = false) -> AnyPublisher<[Movie], APIError> {
        guard let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
              let url = URL(string: "\(baseURL)/search?q=\(encodedQuery)&aggregated=true&includeAdult=\(includeAdult)") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }

        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: SearchResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }

    func fetchAvailableSources(includeAdult: Bool = false) -> AnyPublisher<[VideoSource], APIError> {
        guard let url = URL(string: "\(baseURL)/sources?includeAdult=\(includeAdult)") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }

        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: SourcesResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    func fetchMovieDetail(id: String, source: String = "heimuer") -> AnyPublisher<Movie, APIError> {
        guard let url = URL(string: "\(baseURL)/movie/\(id)?source=\(source)") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: MovieDetailResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success, let movie = response.data {
                    return movie
                } else {
                    throw APIError.serverError(response.error ?? "Movie not found")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    func fetchCategories() -> AnyPublisher<[Category], APIError> {
        guard let url = URL(string: "\(baseURL)/categories") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: CategoriesResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }

    func fetchMoviesByCategory(categoryType: String, page: Int = 1, source: String = "heimuer") -> AnyPublisher<[Movie], APIError> {
        guard let url = URL(string: "\(baseURL)/category/\(categoryType)?page=\(page)&source=\(source)") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }

        return URLSession.shared.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: APIResponse<[Movie]>.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data ?? []
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
}

// MARK: - API Error

enum APIError: Error, LocalizedError {
    case invalidURL
    case networkError
    case decodingError
    case serverError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .networkError:
            return "Network connection error"
        case .decodingError:
            return "Failed to decode response"
        case .serverError(let message):
            return message
        }
    }
}

// MARK: - Mock Service for Preview

class MockAPIService: APIService {
    override func fetchRecommendations() -> AnyPublisher<[Movie], APIError> {
        Just(Movie.sampleList)
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }
    
    override func searchMovies(query: String, source: String = "heimuer") -> AnyPublisher<[Movie], APIError> {
        Just(Movie.sampleList.filter { $0.title.localizedCaseInsensitiveContains(query) })
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }

    override func searchMoviesAggregated(query: String, includeAdult: Bool = false) -> AnyPublisher<[Movie], APIError> {
        Just(Movie.sampleList.filter { $0.title.localizedCaseInsensitiveContains(query) })
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }

    override func fetchAvailableSources(includeAdult: Bool = false) -> AnyPublisher<[VideoSource], APIError> {
        let sources = [
            VideoSource(key: "heimuer", name: "黑木耳", adult: false),
            VideoSource(key: "bfzy", name: "暴风资源", adult: false),
            VideoSource(key: "ruyi", name: "如意资源", adult: false),
            VideoSource(key: "ffzy", name: "非凡影视", adult: false),
            VideoSource(key: "tyyszy", name: "天涯资源", adult: false)
        ]
        return Just(sources)
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }
    
    override func fetchMovieDetail(id: String, source: String = "heimuer") -> AnyPublisher<Movie, APIError> {
        Just(Movie.sample)
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }
    
    override func fetchCategories() -> AnyPublisher<[Category], APIError> {
        let categories = [
            Category(id: 1, name: "电影", type: "1"),
            Category(id: 2, name: "电视剧", type: "2"),
            Category(id: 3, name: "综艺", type: "3"),
            Category(id: 4, name: "动漫", type: "4")
        ]
        return Just(categories)
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }

    override func fetchMoviesByCategory(categoryType: String, page: Int = 1, source: String = "heimuer") -> AnyPublisher<[Movie], APIError> {
        // Return filtered sample movies based on category type
        let filteredMovies = Movie.sampleList.filter { movie in
            switch categoryType {
            case "1": return movie.type?.contains("电影") == true
            case "2": return movie.type?.contains("电视剧") == true || movie.type?.contains("剧集") == true
            case "3": return movie.type?.contains("综艺") == true
            case "4": return movie.type?.contains("动漫") == true || movie.type?.contains("动画") == true
            default: return true
            }
        }

        return Just(filteredMovies)
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }
}
