//
//  Settings.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import Foundation

class SettingsManager: ObservableObject {
    static let shared = SettingsManager()
    
    @Published var adultContentEnabled: Bool {
        didSet {
            UserDefaults.standard.set(adultContentEnabled, forKey: "adultContentEnabled")
        }
    }
    
    @Published var aggregatedSearchEnabled: Bool {
        didSet {
            UserDefaults.standard.set(aggregatedSearchEnabled, forKey: "aggregatedSearchEnabled")
        }
    }
    
    @Published var autoPlayEnabled: Bool {
        didSet {
            UserDefaults.standard.set(autoPlayEnabled, forKey: "autoPlayEnabled")
        }
    }
    
    @Published var adFilteringEnabled: Bool {
        didSet {
            UserDefaults.standard.set(adFilteringEnabled, forKey: "adFilteringEnabled")
        }
    }
    
    private init() {
        self.adultContentEnabled = UserDefaults.standard.bool(forKey: "adultContentEnabled")
        self.aggregatedSearchEnabled = UserDefaults.standard.bool(forKey: "aggregatedSearchEnabled")
        self.autoPlayEnabled = UserDefaults.standard.bool(forKey: "autoPlayEnabled")
        self.adFilteringEnabled = UserDefaults.standard.bool(forKey: "adFilteringEnabled")
        
        // Set default values if not previously set
        if UserDefaults.standard.object(forKey: "aggregatedSearchEnabled") == nil {
            self.aggregatedSearchEnabled = true
        }
        if UserDefaults.standard.object(forKey: "autoPlayEnabled") == nil {
            self.autoPlayEnabled = true
        }
        if UserDefaults.standard.object(forKey: "adFilteringEnabled") == nil {
            self.adFilteringEnabled = true
        }
    }
    
    func resetToDefaults() {
        adultContentEnabled = false
        aggregatedSearchEnabled = true
        autoPlayEnabled = true
        adFilteringEnabled = true
    }
}

struct SettingsSection {
    let title: String
    let items: [SettingsItem]
}

struct SettingsItem {
    let id = UUID()
    let title: String
    let subtitle: String?
    let type: SettingsItemType
    let action: (() -> Void)?
    
    init(title: String, subtitle: String? = nil, type: SettingsItemType, action: (() -> Void)? = nil) {
        self.title = title
        self.subtitle = subtitle
        self.type = type
        self.action = action
    }
}

enum SettingsItemType {
    case toggle(binding: Bool, onChange: (Bool) -> Void)
    case navigation
    case action
    case info(value: String)
}
