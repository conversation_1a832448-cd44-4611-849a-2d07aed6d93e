//
//  Movie.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import Foundation

// MARK: - Movie Models

struct Movie: Codable, Identifiable, Hashable {
    let id: Int
    let title: String
    let poster: String?
    let year: String?
    let area: String?
    let type: String?
    let remarks: String?
    let director: String?
    let actor: String?
    let source: String
    let sourceName: String
    
    // For detail view
    let description: String?
    let episodes: [Episode]?
    
    init(id: Int, title: String, poster: String? = nil, year: String? = nil,
         area: String? = nil, type: String? = nil, remarks: String? = nil,
         director: String? = nil, actor: String? = nil, source: String, 
         sourceName: String, description: String? = nil, episodes: [Episode]? = nil) {
        self.id = id
        self.title = title
        self.poster = poster
        self.year = year
        self.area = area
        self.type = type
        self.remarks = remarks
        self.director = director
        self.actor = actor
        self.source = source
        self.sourceName = sourceName
        self.description = description
        self.episodes = episodes
    }
    
    var displayYear: String {
        year ?? "Unknown"
    }
    
    var displayArea: String {
        area ?? "Unknown"
    }
    
    var displayType: String {
        type ?? "Movie"
    }
    
    var hasEpisodes: Bool {
        episodes?.isEmpty == false
    }
    
    var firstEpisodeURL: String? {
        episodes?.first?.url
    }
}

struct Episode: Codable, Identifiable, Hashable {
    let id = UUID()
    let episode: Int
    let title: String
    let url: String
    
    private enum CodingKeys: String, CodingKey {
        case episode, title, url
    }
    
    var displayTitle: String {
        if title.isEmpty || title == "Episode \(episode)" {
            return "第\(episode)集"
        }
        return title
    }
}

struct Category: Codable, Identifiable, Hashable {
    let id: Int
    let name: String
    let type: String
}

// MARK: - API Response Models

struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let data: T?
    let error: String?
}

struct SearchResponse: Codable {
    let success: Bool
    let data: [Movie]
    let error: String?
}

struct MovieDetailResponse: Codable {
    let success: Bool
    let data: Movie?
    let error: String?
}

struct RecommendationsResponse: Codable {
    let success: Bool
    let data: [Movie]
    let error: String?
}

struct CategoriesResponse: Codable {
    let success: Bool
    let data: [Category]
    let error: String?
}

// MARK: - Extensions

extension Movie {
    static let sample = Movie(
        id: 1,
        title: "Sample Movie",
        poster: "https://example.com/poster.jpg",
        year: "2024",
        area: "美国",
        type: "动作片",
        remarks: "HD",
        director: "Sample Director",
        actor: "Sample Actor",
        source: "sample",
        sourceName: "Sample Source",
        description: "This is a sample movie description for testing purposes.",
        episodes: [
            Episode(episode: 1, title: "Episode 1", url: "https://cdn.ryplay11.com/20250713/181506_66de37e2/index.m3u8"),
            Episode(episode: 2, title: "Episode 2", url: "https://cdn.ryplay11.com/20250713/181505_89c6d4e8/index.m3u8")
        ]
    )
    
    static let sampleList = [
        Movie(id: 1, title: "Action Movie", poster: nil, year: "2024", area: "美国", type: "动作片", remarks: "HD", source: "sample", sourceName: "Sample"),
        Movie(id: 1, title: "Comedy Movie", poster: nil, year: "2023", area: "美国", type: "喜剧片", remarks: "HD", source: "sample", sourceName: "Sample"),
        Movie(id: 1, title: "Drama Series", poster: nil, year: "2024", area: "韩国", type: "电视剧", remarks: "更新至10集", source: "sample", sourceName: "Sample")
    ]
}
