<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "421DC71B-8D85-443C-A29D-05F046D8BAD1"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7911D053-8409-43A4-AA8C-B3E92677C19B"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "TVBrowser/Services/APIService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "39"
            endingLineNumber = "39"
            landmarkName = "fetchRecommendations()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
